// PWA Manager - Logyc Contabilidade
class PWAManager {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.isStandalone = false;
        this.serviceWorkerRegistration = null;
        this.init();
    }
    
    init() {
        this.checkInstallationStatus();
        this.registerServiceWorker();
        this.setupInstallPrompt();
        this.setupUpdateHandler();
        this.setupOfflineHandler();
        this.setupPushNotifications();
        this.createInstallBanner();
    }
    
    checkInstallationStatus() {
        // Check if running as PWA
        this.isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                           window.navigator.standalone ||
                           document.referrer.includes('android-app://');
        
        // Check if already installed
        this.isInstalled = localStorage.getItem('pwa_installed') === 'true' || this.isStandalone;
        
        console.log('PWA Status:', {
            isStandalone: this.isStandalone,
            isInstalled: this.isInstalled
        });
    }
    
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                this.serviceWorkerRegistration = await navigator.serviceWorker.register('/sw.js', {
                    scope: '/'
                });
                
                console.log('PWA: Service Worker registered successfully');
                
                // Listen for updates
                this.serviceWorkerRegistration.addEventListener('updatefound', () => {
                    this.handleServiceWorkerUpdate();
                });
                
                // Check for existing service worker
                if (navigator.serviceWorker.controller) {
                    console.log('PWA: Service Worker is controlling the page');
                }
                
            } catch (error) {
                console.error('PWA: Service Worker registration failed:', error);
            }
        } else {
            console.log('PWA: Service Worker not supported');
        }
    }
    
    setupInstallPrompt() {
        // Listen for beforeinstallprompt event
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('PWA: Install prompt available');
            
            // Prevent the mini-infobar from appearing
            e.preventDefault();
            
            // Save the event for later use
            this.deferredPrompt = e;
            
            // Show custom install banner
            this.showInstallBanner();
        });
        
        // Listen for app installed event
        window.addEventListener('appinstalled', (e) => {
            console.log('PWA: App was installed');
            this.isInstalled = true;
            localStorage.setItem('pwa_installed', 'true');
            this.hideInstallBanner();
            
            // Track installation
            if (window.advancedAnalytics) {
                window.advancedAnalytics.trackEvent('pwa_installed', {
                    timestamp: new Date().toISOString()
                });
            }
            
            // Show success message
            if (window.notifications) {
                window.notifications.success('App instalado com sucesso! Agora você pode acessar a Logyc direto da sua tela inicial.');
            }
        });
    }
    
    createInstallBanner() {
        // Create install banner HTML
        const bannerHTML = `
            <div id="pwa-install-banner" class="pwa-install-banner" style="display: none;">
                <div class="pwa-banner-content">
                    <div class="pwa-banner-icon">📱</div>
                    <div class="pwa-banner-text">
                        <div class="pwa-banner-title">Instalar Logyc App</div>
                        <div class="pwa-banner-subtitle">Acesso rápido direto da sua tela inicial</div>
                    </div>
                    <div class="pwa-banner-actions">
                        <button id="pwa-install-btn" class="pwa-install-btn">Instalar</button>
                        <button id="pwa-dismiss-btn" class="pwa-dismiss-btn">×</button>
                    </div>
                </div>
            </div>
        `;
        
        // Add banner to page
        document.body.insertAdjacentHTML('beforeend', bannerHTML);
        
        // Add styles
        this.addPWAStyles();
        
        // Setup event listeners
        document.getElementById('pwa-install-btn').addEventListener('click', () => {
            this.installApp();
        });
        
        document.getElementById('pwa-dismiss-btn').addEventListener('click', () => {
            this.dismissInstallBanner();
        });
    }
    
    addPWAStyles() {
        const styles = `
            .pwa-install-banner {
                position: fixed;
                bottom: 20px;
                left: 20px;
                right: 20px;
                background: linear-gradient(135deg, #005aec, #0593ff);
                color: white;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0, 90, 236, 0.3);
                z-index: 10000;
                animation: slideInUp 0.5s ease;
                max-width: 400px;
                margin: 0 auto;
            }
            
            .pwa-banner-content {
                display: flex;
                align-items: center;
                padding: 1rem;
                gap: 1rem;
            }
            
            .pwa-banner-icon {
                font-size: 2rem;
                flex-shrink: 0;
            }
            
            .pwa-banner-text {
                flex: 1;
            }
            
            .pwa-banner-title {
                font-weight: bold;
                font-size: 1.1rem;
                margin-bottom: 0.25rem;
            }
            
            .pwa-banner-subtitle {
                font-size: 0.9rem;
                opacity: 0.9;
            }
            
            .pwa-banner-actions {
                display: flex;
                gap: 0.5rem;
                align-items: center;
            }
            
            .pwa-install-btn {
                background: white;
                color: #005aec;
                border: none;
                padding: 0.75rem 1.5rem;
                border-radius: 6px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s;
            }
            
            .pwa-install-btn:hover {
                background: #f0f0f0;
                transform: translateY(-1px);
            }
            
            .pwa-dismiss-btn {
                background: none;
                border: none;
                color: white;
                font-size: 1.5rem;
                cursor: pointer;
                opacity: 0.7;
                transition: opacity 0.3s;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .pwa-dismiss-btn:hover {
                opacity: 1;
            }
            
            .pwa-update-banner {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                background: #ffe206;
                color: #333;
                padding: 1rem;
                text-align: center;
                z-index: 10001;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            
            .pwa-update-btn {
                background: #005aec;
                color: white;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 4px;
                margin-left: 1rem;
                cursor: pointer;
            }
            
            .pwa-offline-indicator {
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: #fd0e35;
                color: white;
                padding: 0.75rem 1rem;
                border-radius: 6px;
                font-size: 0.9rem;
                z-index: 10000;
                animation: slideInRight 0.3s ease;
            }
            
            .pwa-online-indicator {
                background: #01d800;
            }
            
            @keyframes slideInUp {
                from { transform: translateY(100%); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
            
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            
            @media (max-width: 768px) {
                .pwa-install-banner {
                    left: 10px;
                    right: 10px;
                    bottom: 10px;
                }
                
                .pwa-banner-content {
                    padding: 0.75rem;
                }
                
                .pwa-banner-title {
                    font-size: 1rem;
                }
                
                .pwa-banner-subtitle {
                    font-size: 0.8rem;
                }
                
                .pwa-install-btn {
                    padding: 0.5rem 1rem;
                    font-size: 0.9rem;
                }
            }
        `;
        
        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }
    
    showInstallBanner() {
        // Don't show if already installed or dismissed recently
        if (this.isInstalled || this.isStandalone) return;
        
        const dismissedTime = localStorage.getItem('pwa_banner_dismissed');
        if (dismissedTime) {
            const daysSinceDismissed = (Date.now() - parseInt(dismissedTime)) / (1000 * 60 * 60 * 24);
            if (daysSinceDismissed < 7) return; // Don't show for 7 days after dismissal
        }
        
        const banner = document.getElementById('pwa-install-banner');
        if (banner) {
            banner.style.display = 'block';
            
            // Track banner shown
            if (window.advancedAnalytics) {
                window.advancedAnalytics.trackEvent('pwa_banner_shown');
            }
        }
    }
    
    hideInstallBanner() {
        const banner = document.getElementById('pwa-install-banner');
        if (banner) {
            banner.style.display = 'none';
        }
    }
    
    dismissInstallBanner() {
        this.hideInstallBanner();
        localStorage.setItem('pwa_banner_dismissed', Date.now().toString());
        
        // Track dismissal
        if (window.advancedAnalytics) {
            window.advancedAnalytics.trackEvent('pwa_banner_dismissed');
        }
    }
    
    async installApp() {
        if (!this.deferredPrompt) {
            console.log('PWA: No install prompt available');
            return;
        }
        
        try {
            // Show the install prompt
            this.deferredPrompt.prompt();
            
            // Wait for the user's response
            const { outcome } = await this.deferredPrompt.userChoice;
            
            console.log('PWA: Install prompt outcome:', outcome);
            
            // Track user choice
            if (window.advancedAnalytics) {
                window.advancedAnalytics.trackEvent('pwa_install_prompt_result', {
                    outcome: outcome
                });
            }
            
            if (outcome === 'accepted') {
                console.log('PWA: User accepted the install prompt');
            } else {
                console.log('PWA: User dismissed the install prompt');
            }
            
            // Clear the deferred prompt
            this.deferredPrompt = null;
            this.hideInstallBanner();
            
        } catch (error) {
            console.error('PWA: Install failed:', error);
        }
    }
    
    handleServiceWorkerUpdate() {
        const newWorker = this.serviceWorkerRegistration.installing;
        
        newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                console.log('PWA: New service worker available');
                this.showUpdateBanner();
            }
        });
    }
    
    showUpdateBanner() {
        // Remove existing update banner
        const existingBanner = document.getElementById('pwa-update-banner');
        if (existingBanner) {
            existingBanner.remove();
        }
        
        const updateBanner = document.createElement('div');
        updateBanner.id = 'pwa-update-banner';
        updateBanner.className = 'pwa-update-banner';
        updateBanner.innerHTML = `
            <span>🔄 Nova versão disponível!</span>
            <button class="pwa-update-btn" onclick="window.pwaManager.updateApp()">Atualizar</button>
            <button class="pwa-dismiss-btn" onclick="this.parentElement.remove()" style="margin-left: 1rem;">×</button>
        `;
        
        document.body.insertBefore(updateBanner, document.body.firstChild);
    }
    
    updateApp() {
        if (this.serviceWorkerRegistration && this.serviceWorkerRegistration.waiting) {
            // Tell the waiting service worker to skip waiting
            this.serviceWorkerRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
            
            // Reload the page to activate the new service worker
            window.location.reload();
        }
    }
    
    setupUpdateHandler() {
        // Listen for service worker updates
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.addEventListener('controllerchange', () => {
                console.log('PWA: Service worker updated');
                
                // Show notification
                if (window.notifications) {
                    window.notifications.success('App atualizado com sucesso!');
                }
            });
        }
    }
    
    setupOfflineHandler() {
        // Monitor online/offline status
        window.addEventListener('online', () => {
            console.log('PWA: Back online');
            this.showConnectionStatus('online');
            
            // Trigger background sync
            if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
                navigator.serviceWorker.ready.then(registration => {
                    registration.sync.register('form-submission');
                    registration.sync.register('analytics-data');
                });
            }
        });
        
        window.addEventListener('offline', () => {
            console.log('PWA: Gone offline');
            this.showConnectionStatus('offline');
        });
    }
    
    showConnectionStatus(status) {
        // Remove existing indicator
        const existingIndicator = document.getElementById('pwa-connection-indicator');
        if (existingIndicator) {
            existingIndicator.remove();
        }
        
        const indicator = document.createElement('div');
        indicator.id = 'pwa-connection-indicator';
        indicator.className = `pwa-offline-indicator ${status === 'online' ? 'pwa-online-indicator' : ''}`;
        indicator.textContent = status === 'online' ? '🟢 Conectado' : '🔴 Offline';
        
        document.body.appendChild(indicator);
        
        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (indicator.parentElement) {
                indicator.remove();
            }
        }, 3000);
    }
    
    async setupPushNotifications() {
        if ('Notification' in window && 'serviceWorker' in navigator) {
            // Request notification permission
            if (Notification.permission === 'default') {
                // Don't request immediately, wait for user interaction
                setTimeout(() => {
                    this.requestNotificationPermission();
                }, 10000); // Wait 10 seconds
            }
        }
    }
    
    async requestNotificationPermission() {
        try {
            const permission = await Notification.requestPermission();
            console.log('PWA: Notification permission:', permission);
            
            if (permission === 'granted') {
                console.log('PWA: Notifications enabled');
                
                // Track permission granted
                if (window.advancedAnalytics) {
                    window.advancedAnalytics.trackEvent('notification_permission_granted');
                }
                
                // Subscribe to push notifications (would need backend implementation)
                // this.subscribeToPushNotifications();
            }
        } catch (error) {
            console.error('PWA: Notification permission error:', error);
        }
    }
    
    // Public methods
    getInstallationStatus() {
        return {
            isInstalled: this.isInstalled,
            isStandalone: this.isStandalone,
            canInstall: !!this.deferredPrompt
        };
    }
    
    forceShowInstallBanner() {
        localStorage.removeItem('pwa_banner_dismissed');
        this.showInstallBanner();
    }
    
    clearAppData() {
        // Clear all caches and data
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistrations().then(registrations => {
                registrations.forEach(registration => {
                    registration.unregister();
                });
            });
        }
        
        if ('caches' in window) {
            caches.keys().then(names => {
                names.forEach(name => {
                    caches.delete(name);
                });
            });
        }
        
        localStorage.clear();
        sessionStorage.clear();
        
        console.log('PWA: App data cleared');
    }
}

// Initialize PWA Manager
document.addEventListener('DOMContentLoaded', function() {
    window.pwaManager = new PWAManager();
    
    // Add PWA info to global config
    if (window.LogycConfig) {
        window.LogycConfig.pwa = {
            version: '2.0.0',
            features: ['offline', 'installable', 'push-notifications', 'background-sync'],
            manager: window.pwaManager
        };
    }
});

// Export for global use
window.PWAManager = PWAManager;
