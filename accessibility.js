// Melhorias de Acessibilidade para Logyc Contabilidade
document.addEventListener('DOMContentLoaded', function() {
    
    // Adicionar atributos ARIA e melhorar navegação por teclado
    initAccessibility();
    
    // Configurar modo de alto contraste
    initHighContrastMode();
    
    // Configurar navegação por teclado
    initKeyboardNavigation();
    
    // Configurar leitor de tela
    initScreenReaderSupport();
});

function initAccessibility() {
    // Adicionar landmarks ARIA
    const header = document.querySelector('.header');
    if (header) {
        header.setAttribute('role', 'banner');
        header.setAttribute('aria-label', 'Navegação principal');
    }
    
    const nav = document.querySelector('.nav');
    if (nav) {
        nav.setAttribute('role', 'navigation');
        nav.setAttribute('aria-label', 'Menu principal');
    }
    
    const footer = document.querySelector('.footer');
    if (footer) {
        footer.setAttribute('role', 'contentinfo');
        footer.setAttribute('aria-label', 'Informações da empresa');
    }
    
    // Melhorar formulários
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.setAttribute('role', 'form');
        
        // Associar labels com inputs
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            const label = form.querySelector(`label[for="${input.id}"]`);
            if (!label && input.id) {
                // Criar label se não existir
                const labelText = input.getAttribute('placeholder') || input.name;
                if (labelText) {
                    input.setAttribute('aria-label', labelText);
                }
            }
            
            // Adicionar descrições para campos obrigatórios
            if (input.hasAttribute('required')) {
                input.setAttribute('aria-required', 'true');
                input.setAttribute('aria-describedby', `${input.id}-desc`);
                
                // Criar descrição se não existir
                if (!document.getElementById(`${input.id}-desc`)) {
                    const desc = document.createElement('span');
                    desc.id = `${input.id}-desc`;
                    desc.className = 'sr-only';
                    desc.textContent = 'Campo obrigatório';
                    input.parentNode.appendChild(desc);
                }
            }
        });
    });
    
    // Melhorar botões
    const buttons = document.querySelectorAll('button, .btn, .whatsapp-button');
    buttons.forEach(button => {
        if (!button.getAttribute('aria-label') && !button.textContent.trim()) {
            const title = button.getAttribute('title');
            if (title) {
                button.setAttribute('aria-label', title);
            }
        }
        
        // Adicionar role se necessário
        if (button.tagName !== 'BUTTON' && !button.getAttribute('role')) {
            button.setAttribute('role', 'button');
        }
    });
    
    // Melhorar links
    const links = document.querySelectorAll('a');
    links.forEach(link => {
        if (link.getAttribute('target') === '_blank') {
            link.setAttribute('aria-label', 
                `${link.textContent.trim()} (abre em nova janela)`);
        }
    });
}

function initHighContrastMode() {
    // Criar botão de alto contraste
    const contrastButton = document.createElement('button');
    contrastButton.className = 'contrast-toggle';
    contrastButton.innerHTML = '🔆';
    contrastButton.setAttribute('aria-label', 'Alternar modo de alto contraste');
    contrastButton.setAttribute('title', 'Alto Contraste');
    
    // Adicionar estilos do botão
    contrastButton.style.cssText = `
        position: fixed;
        top: 10px;
        left: 10px;
        z-index: 1001;
        background: #000;
        color: #fff;
        border: 2px solid #fff;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        font-size: 16px;
        cursor: pointer;
        display: none;
    `;
    
    document.body.appendChild(contrastButton);
    
    // Verificar se o usuário prefere alto contraste
    if (window.matchMedia('(prefers-contrast: high)').matches) {
        contrastButton.style.display = 'block';
    }
    
    // Toggle alto contraste
    contrastButton.addEventListener('click', function() {
        document.body.classList.toggle('high-contrast');
        
        const isHighContrast = document.body.classList.contains('high-contrast');
        this.innerHTML = isHighContrast ? '🔅' : '🔆';
        this.setAttribute('aria-label', 
            isHighContrast ? 'Desativar alto contraste' : 'Ativar alto contraste');
        
        // Salvar preferência
        localStorage.setItem('highContrast', isHighContrast);
    });
    
    // Restaurar preferência salva
    if (localStorage.getItem('highContrast') === 'true') {
        document.body.classList.add('high-contrast');
        contrastButton.innerHTML = '🔅';
        contrastButton.style.display = 'block';
    }
    
    // Mostrar botão quando necessário
    document.addEventListener('keydown', function(e) {
        if (e.altKey && e.key === 'c') {
            contrastButton.style.display = 'block';
        }
    });
}

function initKeyboardNavigation() {
    // Melhorar navegação por Tab
    const focusableElements = 'a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])';
    
    // Adicionar indicadores visuais de foco
    const style = document.createElement('style');
    style.textContent = `
        .keyboard-focus {
            outline: 3px solid #005aec !important;
            outline-offset: 2px !important;
        }
        
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: #000;
            color: #fff;
            padding: 8px;
            text-decoration: none;
            z-index: 1002;
            border-radius: 4px;
        }
        
        .skip-link:focus {
            top: 6px;
        }
    `;
    document.head.appendChild(style);
    
    // Adicionar link "Pular para conteúdo"
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.className = 'skip-link';
    skipLink.textContent = 'Pular para o conteúdo principal';
    document.body.insertBefore(skipLink, document.body.firstChild);
    
    // Adicionar ID ao conteúdo principal se não existir
    const mainContent = document.querySelector('main, .hero, #inicio');
    if (mainContent && !mainContent.id) {
        mainContent.id = 'main-content';
    }
    
    // Detectar navegação por teclado
    let isKeyboardUser = false;
    
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            isKeyboardUser = true;
            document.body.classList.add('keyboard-navigation');
        }
    });
    
    document.addEventListener('mousedown', function() {
        isKeyboardUser = false;
        document.body.classList.remove('keyboard-navigation');
    });
    
    // Melhorar foco em elementos interativos
    document.addEventListener('focusin', function(e) {
        if (isKeyboardUser) {
            e.target.classList.add('keyboard-focus');
        }
    });
    
    document.addEventListener('focusout', function(e) {
        e.target.classList.remove('keyboard-focus');
    });
    
    // Atalhos de teclado
    document.addEventListener('keydown', function(e) {
        // Alt + H = Home
        if (e.altKey && e.key === 'h') {
            e.preventDefault();
            window.location.href = '/';
        }
        
        // Alt + C = Calculadora
        if (e.altKey && e.key === 'c') {
            e.preventDefault();
            window.location.href = '/calculadora.html';
        }
        
        // Alt + T = Troca de Contabilidade
        if (e.altKey && e.key === 't') {
            e.preventDefault();
            window.location.href = '/troca-contabilidade.html';
        }
        
        // Esc = Fechar modais/alertas
        if (e.key === 'Escape') {
            const alerts = document.querySelectorAll('.error-alert, .success-alert');
            alerts.forEach(alert => alert.remove());
        }
    });
}

function initScreenReaderSupport() {
    // Adicionar regiões ARIA live para anúncios
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.className = 'sr-only';
    liveRegion.id = 'live-region';
    document.body.appendChild(liveRegion);
    
    // Função para anunciar mensagens
    window.announceToScreenReader = function(message) {
        const liveRegion = document.getElementById('live-region');
        if (liveRegion) {
            liveRegion.textContent = message;
            setTimeout(() => {
                liveRegion.textContent = '';
            }, 1000);
        }
    };
    
    // Anunciar mudanças de página
    const pageTitle = document.title;
    announceToScreenReader(`Página carregada: ${pageTitle}`);
    
    // Melhorar formulários para leitores de tela
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            announceToScreenReader('Formulário enviado. Aguarde...');
        });
        
        // Anunciar erros de validação
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('invalid', function() {
                const label = form.querySelector(`label[for="${input.id}"]`);
                const fieldName = label ? label.textContent : input.name;
                announceToScreenReader(`Erro no campo ${fieldName}: ${input.validationMessage}`);
            });
        });
    });
    
    // Melhorar calculadora para leitores de tela
    const calculatorTypes = document.querySelectorAll('.company-type');
    calculatorTypes.forEach(type => {
        type.addEventListener('click', function() {
            const typeName = this.querySelector('h3').textContent;
            announceToScreenReader(`Tipo de empresa selecionado: ${typeName}`);
        });
    });
}

// Adicionar estilos CSS para acessibilidade
const accessibilityCSS = `
    /* Classes para leitores de tela */
    .sr-only {
        position: absolute !important;
        width: 1px !important;
        height: 1px !important;
        padding: 0 !important;
        margin: -1px !important;
        overflow: hidden !important;
        clip: rect(0, 0, 0, 0) !important;
        white-space: nowrap !important;
        border: 0 !important;
    }
    
    /* Alto contraste */
    .high-contrast {
        filter: contrast(150%) brightness(120%);
    }
    
    .high-contrast .whatsapp-button {
        background: #000 !important;
        border: 2px solid #fff !important;
    }
    
    .high-contrast .btn-primary,
    .high-contrast .btn-submit {
        background: #000 !important;
        border: 2px solid #fff !important;
        color: #fff !important;
    }
    
    /* Navegação por teclado */
    .keyboard-navigation *:focus {
        outline: 3px solid #005aec !important;
        outline-offset: 2px !important;
    }
    
    /* Melhorar contraste de texto */
    @media (prefers-contrast: high) {
        .contrast-toggle {
            display: block !important;
        }
    }
    
    /* Reduzir movimento para usuários sensíveis */
    @media (prefers-reduced-motion: reduce) {
        *, *::before, *::after {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
        
        .whatsapp-button {
            animation: none !important;
        }
    }
    
    /* Melhorar legibilidade */
    @media (prefers-color-scheme: dark) {
        .dark-mode-toggle {
            display: block;
        }
    }
`;

// Adicionar CSS ao documento
const accessibilityStyle = document.createElement('style');
accessibilityStyle.textContent = accessibilityCSS;
document.head.appendChild(accessibilityStyle);
