// Sistema de Posts Individuais - Logyc Contabilidade
class PostSystem {
    constructor() {
        this.currentPost = null;
        this.posts = [];
        this.init();
    }

    async init() {
        this.setupStyles();
        try {
            this.posts = await this.getPostsData();
            await this.loadPost();
            this.setupEventListeners();
        } catch (error) {
            console.error('Erro ao inicializar o post:', error);
            this.showError('Não foi possível carregar o artigo.');
        }
    }

    async getPostsData() {
        // Mapeia os arquivos markdown para objetos de post
        const postFiles = [
            {
                file: 'conteudo/post-1-como-abrir-empresa-2024.md',
                category: 'abertura',
                image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=400&fit=crop&auto=format'
            },
            {
                file: 'conteudo/post-2-mei-2024-guia-completo.md',
                category: 'mei',
                image: 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=800&h=400&fit=crop&auto=format'
            },
            {
                file: 'conteudo/post-3-simples-nacional-vantagens.md',
                category: 'fiscal',
                image: 'https://images.unsplash.com/photo-1554224155-8d04cb21cd6c?w=800&h=400&fit=crop&auto=format'
            },
            {
                file: 'conteudo/post-4-departamento-pessoal-obrigacoes.md',
                category: 'pessoal',
                image: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=800&h=400&fit=crop&auto=format'
            },
            {
                file: 'conteudo/post-5-dicas-organizar-contabilidade.md',
                category: 'dicas',
                image: 'https://images.unsplash.com/photo-1554224154-22dec7ec8818?w=800&h=400&fit=crop&auto=format'
            },
            {
                file: 'conteudo/post-6-declaracao-imposto-renda-empresas.md',
                category: 'fiscal',
                image: 'https://images.unsplash.com/photo-1554224155-a1487473ffd9?w=800&h=400&fit=crop&auto=format'
            },
            {
                file: 'conteudo/post-7-trocar-contabilidade-sem-complicacoes.md',
                category: 'dicas',
                image: 'https://images.unsplash.com/photo-1450101499163-c8848c66ca85?w=800&h=400&fit=crop&auto=format'
            },
            {
                file: 'conteudo/post-8-esocial-guia-completo.md',
                category: 'pessoal',
                image: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=800&h=400&fit=crop&auto=format'
            }
        ];

        const posts = [];
        for (const [index, post] of postFiles.entries()) {
            try {
                const content = await this.readMarkdownFile(post.file);
                if (!content) continue;

                // Extrai os metadados do conteúdo markdown
                const title = content.split('\n')[0].replace('# ', '');
                const metaLine = content.split('\n')[2];
                const [author, date, readTime] = metaLine
                    .replace('**', '')
                    .replace('**', '')
                    .split('|')
                    .map(s => s.trim());

                // Extrai o resumo (primeiro parágrafo após o título)
                const excerpt = content.split('\n')[4].trim();

                // Define as tags baseado no conteúdo
                const tags = this.extractTags(content);

                posts.push({
                    id: index + 1,
                    title,
                    excerpt,
                    category: post.category,
                    author: author.replace('Por: ', ''),
                    date: this.formatDateFromText(date.replace('Data: ', '')),
                    readTime: readTime.replace('Tempo de leitura: ', ''),
                    image: post.image,
                    tags,
                    featured: index === 0,
                    contentFile: post.file
                });
            } catch (error) {
                console.error(`Erro ao processar ${post.file}:`, error);
            }
        }

        return posts;
    }

    async readMarkdownFile(path) {
        console.log('Tentando carregar arquivo:', path);
        try {
            // Carrega o arquivo markdown real
            const response = await fetch(path);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const content = await response.text();

            if (!content || content.trim().length === 0) {
                throw new Error('Arquivo vazio ou conteúdo inválido');
            }

            console.log('Arquivo carregado com sucesso:', path);
            return content;
        } catch (error) {
            console.error(`Erro ao carregar arquivo ${path}:`, error);
            throw new Error(`Falha ao carregar ${path}: ${error.message}`);
        }
    }

    formatDateFromText(dateText) {
        const [day, month, year] = dateText.split('/');
        return `${year}-${month}-${day}`;
    }

    extractTags(content) {
        return content.toLowerCase().split('\n')
            .filter(line => line.startsWith('## ') || line.startsWith('### '))
            .map(line => line.replace(/^#+ /, '').toLowerCase())
            .slice(0, 4);
    }

    setupStyles() {
        const styles = `
            .breadcrumb-section {
                background: var(--cinza-claro, #f8f9fa);
                padding: 1rem 0;
                border-bottom: 1px solid #e9ecef;
            }

            .breadcrumb {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.9rem;
                color: var(--cinza-escuro, #666);
            }

            .breadcrumb a {
                color: var(--azul-escuro, #005aec);
                text-decoration: none;
            }

            .breadcrumb a:hover {
                text-decoration: underline;
            }

            .article-section {
                padding: 2rem 0 4rem;
                background: white;
            }

            .article-container {
                display: grid;
                grid-template-columns: 1fr 300px;
                gap: 3rem;
                max-width: 1200px;
                margin: 0 auto;
                padding: 0 1rem;
            }

            .article-main {
                max-width: 800px;
            }

            .article-sidebar {
                position: sticky;
                top: 2rem;
                height: fit-content;
            }

            .sidebar-widget {
                background: white;
                border-radius: 12px;
                padding: 1.5rem;
                margin-bottom: 2rem;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .sidebar-widget h3 {
                color: var(--azul-escuro, #005aec);
                margin-bottom: 1rem;
                font-size: 1.2rem;
            }

            .related-posts {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            .related-post-item {
                display: flex;
                gap: 1rem;
                text-decoration: none;
                color: inherit;
                padding: 0.5rem;
                border-radius: 8px;
                transition: background 0.3s;
            }

            .related-post-item:hover {
                background: var(--cinza-claro, #f8f9fa);
            }

            .related-post-image {
                width: 60px;
                height: 60px;
                object-fit: cover;
                border-radius: 6px;
                flex-shrink: 0;
            }

            .related-post-content {
                flex: 1;
            }

            .related-post-title {
                font-size: 0.9rem;
                font-weight: bold;
                color: var(--azul-escuro, #005aec);
                line-height: 1.3;
                margin-bottom: 0.25rem;
            }

            .related-post-date {
                font-size: 0.8rem;
                color: var(--cinza-escuro, #666);
            }

            .newsletter-widget {
                background: linear-gradient(135deg, var(--azul-escuro, #005aec), var(--azul-claro, #0066ff));
                color: white;
            }

            .newsletter-widget h3 {
                color: white;
            }

            .newsletter-widget p {
                margin-bottom: 1rem;
                opacity: 0.9;
            }

            .newsletter-widget .newsletter-form {
                display: flex;
                flex-direction: column;
                gap: 0.5rem;
            }

            .newsletter-widget input {
                padding: 0.75rem;
                border: none;
                border-radius: 6px;
                font-size: 0.9rem;
            }

            .newsletter-widget button {
                background: var(--amarelo, #ffe206);
                color: var(--azul-escuro, #005aec);
                border: none;
                padding: 0.75rem;
                border-radius: 6px;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s;
            }

            .newsletter-widget button:hover {
                background: #ffd700;
                transform: translateY(-1px);
            }

            .contact-widget {
                text-align: center;
            }

            .contact-widget p {
                margin-bottom: 1.5rem;
                color: var(--cinza-escuro, #666);
            }

            .btn-contact {
                display: inline-block;
                background: var(--verde-claro, #01d800);
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 25px;
                text-decoration: none;
                font-weight: bold;
                transition: all 0.3s;
            }

            .btn-contact:hover {
                background: var(--verde-escuro, #217345);
                transform: translateY(-2px);
            }

            .article-header {
                margin-bottom: 2rem;
            }

            .article-meta {
                display: flex;
                align-items: center;
                gap: 1rem;
                margin-bottom: 1rem;
                font-size: 0.9rem;
                flex-wrap: wrap;
            }

            .article-category {
                background: var(--azul-escuro, #005aec);
                color: white;
                padding: 4px 12px;
                border-radius: 15px;
                font-size: 0.8rem;
                text-transform: uppercase;
                font-weight: bold;
            }

            .article-date,
            .article-read-time {
                color: var(--cinza-escuro, #666);
            }

            .article-title {
                font-size: 2.5rem;
                font-weight: bold;
                color: var(--azul-escuro, #005aec);
                line-height: 1.2;
                margin-bottom: 1rem;
            }

            .article-excerpt {
                font-size: 1.2rem;
                color: var(--cinza-escuro, #666);
                line-height: 1.6;
                margin-bottom: 1rem;
            }

            .article-author {
                color: var(--cinza-escuro, #666);
                font-style: italic;
            }

            .article-image-container {
                margin-bottom: 2rem;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }

            .article-image {
                width: 100%;
                height: 400px;
                object-fit: cover;
                display: block;
            }

            .article-content {
                font-size: 1.1rem;
                line-height: 1.8;
                color: #333;
                margin-bottom: 2rem;
            }

            .article-content h1,
            .article-content h2,
            .article-content h3,
            .article-content h4 {
                color: var(--azul-escuro, #005aec);
                margin-top: 2rem;
                margin-bottom: 1rem;
            }

            .article-content h2 {
                font-size: 1.8rem;
                border-bottom: 2px solid var(--amarelo, #ffe206);
                padding-bottom: 0.5rem;
            }

            .article-content h3 {
                font-size: 1.4rem;
            }

            .article-content h4 {
                font-size: 1.2rem;
            }

            .article-content p {
                margin-bottom: 1.5rem;
            }

            .article-content ul,
            .article-content ol {
                margin-bottom: 1.5rem;
                padding-left: 2rem;
            }

            .article-content li {
                margin-bottom: 0.5rem;
            }

            .article-content strong {
                color: var(--azul-escuro, #005aec);
                font-weight: bold;
            }

            .article-content blockquote {
                border-left: 4px solid var(--amarelo, #ffe206);
                padding-left: 1rem;
                margin: 1.5rem 0;
                font-style: italic;
                background: var(--cinza-claro, #f8f9fa);
                padding: 1rem;
                border-radius: 0 8px 8px 0;
            }

            .article-tags {
                margin-bottom: 2rem;
                padding-top: 1rem;
                border-top: 1px solid #e9ecef;
            }

            .article-tag {
                display: inline-block;
                background: var(--cinza-claro, #f8f9fa);
                color: var(--azul-escuro, #005aec);
                padding: 4px 12px;
                border-radius: 15px;
                font-size: 0.9rem;
                margin: 0.25rem 0.5rem 0.25rem 0;
                text-decoration: none;
                border: 1px solid #e9ecef;
                transition: all 0.3s;
            }

            .article-tag:hover {
                background: var(--azul-escuro, #005aec);
                color: white;
            }

            .article-navigation {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 3rem;
                padding-top: 2rem;
                border-top: 2px solid var(--cinza-claro, #f8f9fa);
                flex-wrap: wrap;
                gap: 1rem;
            }

            .btn-back {
                background: var(--azul-escuro, #005aec);
                color: white;
                padding: 12px 24px;
                border-radius: 25px;
                text-decoration: none;
                font-weight: bold;
                transition: all 0.3s;
            }

            .btn-back:hover {
                background: var(--azul-claro, #0066ff);
                transform: translateY(-2px);
            }

            .article-nav-posts {
                display: flex;
                gap: 1rem;
                flex-wrap: wrap;
            }

            .nav-post {
                display: flex;
                flex-direction: column;
                padding: 1rem;
                border: 1px solid #e9ecef;
                border-radius: 8px;
                text-decoration: none;
                color: inherit;
                transition: all 0.3s;
                max-width: 200px;
            }

            .nav-post:hover {
                border-color: var(--azul-escuro, #005aec);
                transform: translateY(-2px);
            }

            .nav-label {
                font-size: 0.8rem;
                color: var(--cinza-escuro, #666);
                text-transform: uppercase;
                margin-bottom: 0.5rem;
            }

            .nav-title {
                font-weight: bold;
                color: var(--azul-escuro, #005aec);
                font-size: 0.9rem;
                line-height: 1.3;
            }

            .related-articles-section {
                background: var(--cinza-claro, #f8f9fa);
                padding: 3rem 0;
            }

            .related-articles-section h2 {
                text-align: center;
                color: var(--azul-escuro, #005aec);
                margin-bottom: 2rem;
                font-size: 2rem;
            }

            .related-articles-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 2rem;
                max-width: 1200px;
                margin: 0 auto;
            }

            .related-article-card {
                background: white;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                transition: all 0.3s;
                text-decoration: none;
                color: inherit;
            }

            .related-article-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }

            .related-article-image {
                width: 100%;
                height: 200px;
                object-fit: cover;
            }

            .related-article-content {
                padding: 1.5rem;
            }

            .related-article-title {
                font-size: 1.1rem;
                font-weight: bold;
                color: var(--azul-escuro, #005aec);
                margin-bottom: 0.5rem;
                line-height: 1.4;
            }

            .related-article-excerpt {
                color: var(--cinza-escuro, #666);
                font-size: 0.9rem;
                line-height: 1.5;
            }

            @media (max-width: 768px) {
                .article-container {
                    grid-template-columns: 1fr;
                    gap: 2rem;
                }

                .article-sidebar {
                    position: static;
                    order: -1;
                }

                .sidebar-widget {
                    margin-bottom: 1rem;
                }

                .article-title {
                    font-size: 2rem;
                }

                .article-excerpt {
                    font-size: 1.1rem;
                }

                .article-content {
                    font-size: 1rem;
                }

                .article-navigation {
                    flex-direction: column;
                    align-items: stretch;
                }

                .article-nav-posts {
                    justify-content: center;
                }

                .nav-post {
                    max-width: none;
                    flex: 1;
                }

                .related-articles-grid {
                    grid-template-columns: 1fr;
                }
            }
        `;

        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }

    getPostIdFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        return parseInt(urlParams.get('id')) || 1;
    }

    async loadPost() {
        const postId = this.getPostIdFromUrl();
        this.currentPost = this.posts.find(post => post.id === postId);

        if (!this.currentPost) {
            this.showError('Post não encontrado');
            return;
        }

        this.updatePageMeta();
        this.renderPostHeader();
        await this.loadPostContent();
        this.renderPostTags();
        this.renderNavigation();
        this.renderRelatedPosts();
    }

    updatePageMeta() {
        const post = this.currentPost;

        // Atualizar título da página
        document.title = `${post.title} - Logyc Contabilidade`;

        // Atualizar meta description
        document.querySelector('meta[name="description"]').content = post.excerpt;

        // Atualizar Open Graph
        document.querySelector('meta[property="og:title"]').content = post.title;
        document.querySelector('meta[property="og:description"]').content = post.excerpt;
        document.querySelector('meta[property="og:image"]').content = post.image;

        // Atualizar Twitter
        document.querySelector('meta[property="twitter:title"]').content = post.title;
        document.querySelector('meta[property="twitter:description"]').content = post.excerpt;
        document.querySelector('meta[property="twitter:image"]').content = post.image;

        // Atualizar Schema
        const schema = {
            "@context": "https://schema.org",
            "@type": "Article",
            "headline": post.title,
            "description": post.excerpt,
            "image": post.image,
            "datePublished": post.date,
            "author": {
                "@type": "Person",
                "name": post.author
            },
            "publisher": {
                "@type": "Organization",
                "name": "Logyc Contabilidade",
                "logo": "https://logyccontabilidade.com.br/logo.png"
            }
        };

        document.getElementById('article-schema').textContent = JSON.stringify(schema);

        // Atualizar breadcrumb
        document.getElementById('breadcrumb-title').textContent = post.title;
    }

    renderPostHeader() {
        const post = this.currentPost;

        document.getElementById('article-category').textContent = this.getCategoryName(post.category);
        document.getElementById('article-date').textContent = this.formatDate(post.date);
        document.getElementById('article-read-time').textContent = post.readTime;
        document.getElementById('article-title').textContent = post.title;
        document.getElementById('article-excerpt').textContent = post.excerpt;
        document.getElementById('article-author').textContent = post.author;

        const imageEl = document.getElementById('article-image');
        imageEl.src = post.image;
        imageEl.alt = post.title;
        imageEl.onerror = () => {
            imageEl.src = 'https://via.placeholder.com/800x400/005aec/ffffff?text=Logyc+Contabilidade';
        };
    }

    async loadPostContent() {
        if (!this.currentPost) return;

        try {
            const content = await this.readMarkdownFile(this.currentPost.contentFile);
            if (!content) throw new Error('Conteúdo não encontrado');

            // Remove os metadados (título, autor, data) e pega apenas o conteúdo
            const contentLines = content.split('\n');
            const mainContent = contentLines.slice(5).join('\n'); // Pula as 5 primeiras linhas

            const html = this.markdownToHtml(mainContent);
            document.getElementById('article-content').innerHTML = html;
        } catch (error) {
            console.error('Erro ao carregar conteúdo:', error);
            document.getElementById('article-content').innerHTML = `
                <p>Desculpe, não foi possível carregar o conteúdo deste artigo.</p>
                <p>Entre em contato conosco para mais informações.</p>
            `;
        }
    }

    markdownToHtml(markdown) {
        let html = markdown;

        // Headers (processa na ordem inversa para evitar conflitos)
        html = html.replace(/^#### (.*$)/gm, '<h4>$1</h4>');
        html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
        html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
        html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');

        // Strong/Bold (não-guloso para pegar cada par corretamente)
        html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

        // Emphasis/Italic (não-guloso)
        html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

        // Lists (unordered)
        html = html.replace(/^\s*[-*]\s+(.+)/gm, '<li>$1</li>');
        html = html.replace(/(?:^<li>.*?<\/li>\n?)+/gms, function(match) {
            return '<ul>' + match.trim() + '</ul>';
        });

        // Lists (ordered)
        html = html.replace(/^\s*\d+\.\s+(.+)/gm, '<li>$1</li>');
        html = html.replace(/(?:^<li>.*?<\/li>\n?)+/gms, function(match) {
            return match.startsWith('<ul>') ? match : '<ol>' + match.trim() + '</ol>';
        });

        // Links (com target="_blank" e rel="noopener noreferrer" para segurança)
        html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');

        // Blockquotes (suporta múltiplas linhas)
        html = html.replace(/^\>\s*(.+)/gm, '<blockquote>$1</blockquote>');
        html = html.replace(/(?:^<blockquote>.*?<\/blockquote>\n?)+/gms, function(match) {
            return '<div class="blockquote-container">' + match.trim() + '</div>';
        });

        // Code blocks e inline code
        html = html.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');
        html = html.replace(/`([^`]+)`/g, '<code>$1</code>');

        // Horizontal rules
        html = html.replace(/^---$/gm, '<hr>');

        // Paragraphs e line breaks (processa por último para não interferir com outros elementos)
        html = html.replace(/\n\n/g, '</p><p>');
        html = html.replace(/([^>\n])\n([^<])/g, '$1<br>$2');

        // Wrap em parágrafos se ainda não estiver
        if (!html.startsWith('<')) {
            html = '<p>' + html + '</p>';
        }

        // Remove parágrafos vazios e limpa
        html = html.replace(/<p>\s*<\/p>/g, '')
            .replace(/<p><br><\/p>/g, '<br>')
            .trim();

        return html;
    }

    renderPostTags() {
        const tagsContainer = document.getElementById('article-tags');
        const tags = this.currentPost.tags;

        tagsContainer.innerHTML = tags.map(tag =>
            `<a href="blog.html?tag=${encodeURIComponent(tag)}" class="article-tag">#${tag}</a>`
        ).join('');
    }

    renderNavigation() {
        const currentIndex = this.posts.findIndex(post => post.id === this.currentPost.id);
        const prevPost = this.posts[currentIndex + 1];
        const nextPost = this.posts[currentIndex - 1];

        const prevEl = document.getElementById('prev-post');
        const nextEl = document.getElementById('next-post');

        if (prevPost) {
            prevEl.href = `post.html?id=${prevPost.id}`;
            prevEl.querySelector('.nav-title').textContent = prevPost.title;
            prevEl.style.display = 'flex';
        }

        if (nextPost) {
            nextEl.href = `post.html?id=${nextPost.id}`;
            nextEl.querySelector('.nav-title').textContent = nextPost.title;
            nextEl.style.display = 'flex';
        }
    }

    renderRelatedPosts() {
        const relatedPosts = this.posts
            .filter(post => post.id !== this.currentPost.id && post.category === this.currentPost.category)
            .slice(0, 3);

        if (relatedPosts.length === 0) {
            relatedPosts.push(...this.posts.filter(post => post.id !== this.currentPost.id).slice(0, 3));
        }

        const container = document.getElementById('related-articles-grid');
        container.innerHTML = relatedPosts.map(post => `
            <a href="post.html?id=${post.id}" class="related-article-card">
                <img src="${post.image}" alt="${post.title}" class="related-article-image"
                     onerror="this.src='https://via.placeholder.com/400x200/005aec/ffffff?text=Logyc+Contabilidade'">
                <div class="related-article-content">
                    <h3 class="related-article-title">${post.title}</h3>
                    <p class="related-article-excerpt">${post.excerpt}</p>
                </div>
            </a>
        `).join('');
    }

    setupEventListeners() {
        // Newsletter form
        const newsletterForm = document.getElementById('sidebarNewsletterForm');
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleNewsletterSubmit(e);
            });
        }
    }

    handleNewsletterSubmit(e) {
        const email = e.target.querySelector('input[type="email"]').value;

        if (window.notifications) {
            window.notifications.success('Email cadastrado com sucesso! Você receberá nossas novidades em breve.');
        }

        if (window.trackEvent) {
            window.trackEvent('newsletter_signup', {
                email_domain: email.split('@')[1],
                source: 'post_sidebar'
            });
        }

        e.target.reset();
    }

    getCategoryName(category) {
        const names = {
            'mei': 'MEI',
            'abertura': 'Abertura',
            'fiscal': 'Fiscal',
            'pessoal': 'Pessoal',
            'dicas': 'Dicas'
        };
        return names[category] || category;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('pt-BR');
    }

    showError(message) {
        document.getElementById('article-content').innerHTML = `
            <div style="text-align: center; padding: 3rem;">
                <h2>Ops! ${message}</h2>
                <p>Volte ao blog para ver outros artigos interessantes.</p>
                <a href="blog.html" class="btn-back">← Voltar ao Blog</a>
            </div>
        `;
    }
}

// Inicializar apenas na página de post
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.pathname.includes('post.html')) {
        window.postSystem = new PostSystem();
    }
});

// Exportar para uso global
window.PostSystem = PostSystem;
