/* Importação de fontes modernas */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* Reset e configurações básicas */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1a1a1a;
    background: #ffffff;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Paleta de cores moderna - Baseada na identidade visual Logyc */
:root {
    /* Cores principais da identidade visual */
    --primary-blue: #005aec;
    --primary-blue-dark: #004bb8;
    --primary-blue-light: #0593ff;
    --secondary-green: #01d800;
    --secondary-green-dark: #217345;
    --accent-yellow: #ffe206;
    --accent-red: #fd0e35;

    /* Cores neutras modernas */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Gradientes modernos */
    --gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-green) 0%, var(--secondary-green-dark) 100%);
    --gradient-hero: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-dark) 30%, var(--secondary-green-dark) 70%, var(--secondary-green) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-yellow) 0%, #ffed4e 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);

    /* Sombras aprimoradas */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --shadow-colored: 0 10px 25px -5px rgba(0, 90, 236, 0.3);
    --shadow-green: 0 10px 25px -5px rgba(1, 216, 0, 0.3);

    /* Transições suaves */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Bordas modernas */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-3xl: 2rem;
    --radius-full: 9999px;

    /* Espaçamentos */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;

    /* Compatibilidade com cores antigas */
    --verde-claro: var(--secondary-green);
    --verde-escuro: var(--secondary-green-dark);
    --vermelho: var(--accent-red);
    --amarelo: var(--accent-yellow);
    --azul-claro: var(--primary-blue-light);
    --azul-escuro: var(--primary-blue-dark);
    --branco: var(--white);
    --cinza-claro: var(--gray-50);
    --cinza-escuro: var(--gray-700);
}

/* Utilitários globais e animações modernas */
.fade-in {
    animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up {
    animation: slideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-left {
    animation: slideInLeft 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-in-right {
    animation: slideInRight 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-in {
    animation: scaleIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.bounce-in {
    animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.glass-effect {
    background: var(--gradient-glass);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.hover-lift {
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.hover-glow {
    transition: box-shadow var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: var(--shadow-colored);
}

.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-green {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Keyframes para animações */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(40px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(40px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounceIn {
    from {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
    }
}

@keyframes floatParticle {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Header moderno */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    transition: all var(--transition-normal);
}

.header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
}

.header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 20px;
    position: relative;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    z-index: 1001;
}

.logo img {
    height: 50px;
    width: auto;
    transition: transform var(--transition-normal);
}

.logo:hover img {
    transform: scale(1.05);
}

.logo-text {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 1.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Navegação desktop */
.nav {
    display: flex;
    align-items: center;
}

.nav ul {
    display: flex;
    list-style: none;
    gap: 2.5rem;
    margin: 0;
    padding: 0;
}

.nav li {
    position: relative;
}

.nav a {
    text-decoration: none;
    color: var(--gray-700);
    font-weight: 500;
    font-size: 0.95rem;
    padding: 0.5rem 0;
    position: relative;
    transition: all var(--transition-normal);
}

.nav a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--transition-normal);
}

.nav a:hover,
.nav a.active {
    color: var(--primary-blue);
}

.nav a:hover::after,
.nav a.active::after {
    width: 100%;
}

/* Botão CTA do header */
.header-cta {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.btn-troca {
    background: var(--gradient-secondary);
    color: var(--white);
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.9rem;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.btn-troca::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-troca:hover::before {
    left: 100%;
}

.btn-troca:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-troca.active {
    background: var(--secondary-green-dark);
}

/* Menu mobile */
.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    z-index: 1001;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--gray-700);
    margin: 3px 0;
    transition: all var(--transition-normal);
    border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
    transform: rotate(-45deg) translate(-5px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
    opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
    transform: rotate(45deg) translate(-5px, -6px);
}

.mobile-nav {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(20px);
    z-index: 1000;
    padding-top: 100px;
}

.mobile-nav.active {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

.mobile-nav ul {
    flex-direction: column;
    gap: 0;
    padding: 2rem;
}

.mobile-nav li {
    border-bottom: 1px solid var(--gray-200);
}

.mobile-nav a {
    display: block;
    padding: 1.5rem 0;
    font-size: 1.1rem;
    font-weight: 500;
}

.mobile-nav .btn-troca {
    margin: 2rem 0;
    display: inline-block;
    text-align: center;
}

/* Hero Section Ultra Moderno */
.hero {
    background: var(--gradient-hero);
    color: var(--white);
    padding: 140px 0 100px;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
    will-change: transform;
    transform: translate3d(0, 0, 0);
}

/* Esconder seta indicativa no desktop */
.scroll-indicator {
    display: none;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(1, 216, 0, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(5, 147, 255, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 226, 6, 0.2) 0%, transparent 50%);
    animation: float 20s ease-in-out infinite;
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

/* Partículas flutuantes */
.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.particle {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: floatParticle 15s infinite linear;
}

.particle:nth-child(1) {
    width: 4px;
    height: 4px;
    left: 10%;
    animation-delay: 0s;
    animation-duration: 20s;
}

.particle:nth-child(2) {
    width: 6px;
    height: 6px;
    left: 20%;
    animation-delay: 2s;
    animation-duration: 25s;
}

.particle:nth-child(3) {
    width: 3px;
    height: 3px;
    left: 30%;
    animation-delay: 4s;
    animation-duration: 18s;
}

.particle:nth-child(4) {
    width: 5px;
    height: 5px;
    left: 40%;
    animation-delay: 6s;
    animation-duration: 22s;
}

.particle:nth-child(5) {
    width: 4px;
    height: 4px;
    left: 50%;
    animation-delay: 8s;
    animation-duration: 20s;
}

.particle:nth-child(6) {
    width: 6px;
    height: 6px;
    left: 60%;
    animation-delay: 10s;
    animation-duration: 24s;
}

.particle:nth-child(7) {
    width: 3px;
    height: 3px;
    left: 70%;
    animation-delay: 12s;
    animation-duration: 19s;
}

.particle:nth-child(8) {
    width: 5px;
    height: 5px;
    left: 80%;
    animation-delay: 14s;
    animation-duration: 21s;
}

.particle:nth-child(9) {
    width: 4px;
    height: 4px;
    left: 90%;
    animation-delay: 16s;
    animation-duration: 23s;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 900px;
    margin: 0 auto;
    animation: slideUp 1s ease-out;
}

.hero h1 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.2;
    background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.hero-subtitle {
    font-size: clamp(1.1rem, 2.5vw, 1.4rem);
    margin-bottom: 2.5rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.95;
    line-height: 1.6;
    font-weight: 400;
}

.hero-cta {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    margin-top: 3rem;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.btn-primary {
    background: var(--gradient-secondary);
    color: var(--white);
    padding: 1.2rem 2.5rem;
    text-decoration: none;
    border-radius: var(--radius-xl);
    font-size: 1.1rem;
    font-weight: 600;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--white);
    padding: 1.2rem 2.5rem;
    text-decoration: none;
    border-radius: var(--radius-xl);
    font-size: 1.1rem;
    font-weight: 600;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
    border-color: rgba(255, 255, 255, 0.3);
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    margin-top: 4rem;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1rem;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    min-height: 140px;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-yellow) 0%, var(--accent-green) 100%);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--accent-yellow) 0%, var(--accent-green) 100%);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    flex-shrink: 0;
    transition: all var(--transition-normal);
    margin-bottom: 0.5rem;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.3);
}

.stat-content {
    text-align: center;
}

.stat-number {
    font-family: 'Poppins', sans-serif;
    font-size: 1.8rem;
    font-weight: 800;
    display: block;
    margin-bottom: 0.25rem;
    color: var(--white);
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--white);
    opacity: 0.95;
    display: block;
    margin-bottom: 0.25rem;
}

.stat-description {
    font-size: 0.75rem;
    color: var(--white);
    opacity: 0.7;
    line-height: 1.3;
}

/* Scroll indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(255, 255, 255, 0.7);
    animation: bounce 2s infinite;
    cursor: pointer;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

/* Sobre Section Moderna */
.sobre {
    padding: 60px 0;
    background: var(--gray-50);
    position: relative;
}

.sobre::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--gray-200), transparent);
}

.sobre-header {
    text-align: center;
    margin-bottom: 5rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.sobre h2 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--gray-900);
    position: relative;
}

.sobre h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.sobre-content p {
    font-size: 1.2rem;
    text-align: center;
    margin-bottom: 2rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    color: var(--gray-600);
    line-height: 1.8;
}

.sobre-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    margin-top: 4rem;
}

.sobre-item {
    background: var(--white);
    padding: 2.5rem 1.5rem;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    text-align: center;
    position: relative;
    transition: all var(--transition-normal);
    border: 1px solid var(--gray-100);
    overflow: hidden;
}

.sobre-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.sobre-item:hover::before {
    transform: scaleX(1);
}

.sobre-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.sobre-item-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 1.5rem;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--white);
    position: relative;
}

.sobre-item-icon::after {
    content: '';
    position: absolute;
    inset: -4px;
    background: var(--gradient-primary);
    border-radius: 50%;
    z-index: -1;
    opacity: 0.2;
    animation: pulse-ring 2s infinite;
}

@keyframes pulse-ring {
    0% {
        transform: scale(1);
        opacity: 0.2;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.1;
    }
    100% {
        transform: scale(1);
        opacity: 0.2;
    }
}

.sobre-item h3 {
    color: var(--gray-900);
    margin-bottom: 1.25rem;
    font-size: 1.2rem;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
    line-height: 1.3;
}

.sobre-item p {
    color: var(--gray-600);
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Seção de valores/diferenciais */
.valores-section {
    margin-top: 3rem;
    text-align: center;
}

.valores-title {
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 2rem;
}

.valores-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.valor-item {
    padding: 2rem 1.5rem;
    background: linear-gradient(135deg, var(--white) 0%, var(--gray-50) 100%);
    border-radius: var(--radius-xl);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.valor-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-blue);
}

.valor-numero {
    font-family: 'Poppins', sans-serif;
    font-size: 3rem;
    font-weight: 800;
    color: var(--primary-blue);
    display: block;
    margin-bottom: 0.5rem;
}

.valor-label {
    font-size: 0.9rem;
    color: var(--gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
}

/* Serviços Section Moderna */
.servicos {
    padding: 60px 0;
    background: var(--white);
    position: relative;
}

.servicos::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--gray-200), transparent);
}

.servicos-header {
    text-align: center;
    margin-bottom: 5rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.servicos h2 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--gray-900);
    position: relative;
}

.servicos h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-secondary);
    border-radius: 2px;
}

.servicos-subtitle {
    font-size: 1.2rem;
    color: var(--gray-600);
    line-height: 1.8;
    margin-top: 2rem;
}

.servicos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2.5rem;
    margin-top: 4rem;
}

.servico-item {
    background: var(--white);
    padding: 3rem 2.5rem;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-100);
    position: relative;
    transition: all var(--transition-normal);
    overflow: hidden;
}

.servico-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-secondary);
    transform: scaleY(0);
    transition: transform var(--transition-normal);
    transform-origin: bottom;
}

.servico-item:hover::before {
    transform: scaleY(1);
}

.servico-item:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--secondary-green);
}

.servico-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.servico-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-secondary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--white);
    flex-shrink: 0;
}

.servico-item h3 {
    color: var(--gray-900);
    font-size: 1.4rem;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
    margin: 0;
}

.servico-description {
    color: var(--gray-600);
    margin-bottom: 2rem;
    line-height: 1.7;
}

.servico-item ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.servico-item li {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-100);
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    color: var(--gray-700);
    line-height: 1.6;
    transition: all var(--transition-fast);
}

.servico-item li:last-child {
    border-bottom: none;
}

.servico-item li:hover {
    color: var(--gray-900);
    padding-left: 0.5rem;
}

.servico-item li::before {
    content: "";
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='%2301d800'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    flex-shrink: 0;
    margin-top: 0.2rem;
}

/* CTA Section dentro de serviços */
.servicos-cta {
    margin-top: 5rem;
    text-align: center;
    padding: 3rem 2rem;
    background: var(--gradient-primary);
    border-radius: var(--radius-2xl);
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.servicos-cta::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 15s ease-in-out infinite;
}

.servicos-cta h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.servicos-cta p {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.servicos-cta .btn-primary {
    background: var(--white);
    color: var(--primary-blue);
}

.servicos-cta .btn-primary:hover {
    background: var(--gray-100);
    transform: translateY(-3px);
}

/* Formulário de Contato Moderno */
.contato {
    padding: 60px 0;
    background: var(--gray-50);
    position: relative;
}

.contato::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--gray-200), transparent);
}

.contato-header {
    text-align: center;
    margin-bottom: 5rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.contato-header h2 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--gray-900);
    position: relative;
}

.contato-header h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

.contato-subtitle {
    font-size: 1.2rem;
    color: var(--gray-600);
    line-height: 1.8;
    margin-top: 2rem;
}

.contato-wrapper {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: start;
}

/* Informações de contato */
.contato-info {
    background: var(--white);
    padding: 3rem 2rem;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-100);
    height: fit-content;
    position: sticky;
    top: 120px;
}

.info-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 2rem;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    flex-shrink: 0;
}

.info-content h4 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.info-content p {
    color: var(--gray-600);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Formulário principal */
.contact-form {
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-100);
    overflow: hidden;
    position: relative;
}

.form-step {
    padding: 3rem;
    display: none;
    animation: slideInRight 0.3s ease-out;
}

.form-step.active {
    display: block;
}

.form-step h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.step-description {
    color: var(--gray-600);
    margin-bottom: 2rem;
    font-size: 1rem;
}

/* Form rows e grupos */
.form-row {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.75rem;
    font-size: 1rem;
    transition: all var(--transition-normal);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    background: var(--white);
    font-size: 1rem;
    color: var(--gray-900);
    transition: all var(--transition-normal);
    outline: none;
    font-family: inherit;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 102, 255, 0.1);
    transform: translateY(-1px);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--gray-400);
    font-size: 0.95rem;
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
    line-height: 1.6;
}

.form-line {
    display: none; /* Removido para o novo design */
}

.form-group.focused label {
    color: var(--primary-blue);
}

.form-title {
    text-align: center;
    margin-bottom: 3rem;
}

.form-title h2 {
    background: var(--amarelo);
    color: var(--cinza-escuro);
    padding: 1rem;
    border-radius: 5px;
    font-weight: bold;
    display: inline-block;
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
    background: var(--branco);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.form-group label {
    min-width: 150px;
    font-weight: bold;
}

.form-group label .required,
.required {
    color: var(--vermelho);
}

.form-group input,
.form-group textarea {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.required-label {
    font-weight: bold;
    margin-bottom: 1rem;
    display: block;
}

.radio-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-left: 1rem;
}

.radio-group label {
    min-width: auto;
    font-weight: normal;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-submit {
    background: var(--verde-claro);
    color: var(--branco);
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.3s;
    margin-top: 1rem;
}

.btn-submit:hover {
    background: var(--verde-escuro);
}

.observacoes {
    max-width: 600px;
    margin: 2rem auto 0;
    background: var(--branco);
    padding: 2rem;
    border-radius: 10px;
}

.observacoes h3 {
    background: var(--amarelo);
    color: var(--cinza-escuro);
    padding: 0.5rem 1rem;
    border-radius: 5px;
    text-align: center;
    margin-bottom: 1rem;
}

.observacoes ol {
    padding-left: 1rem;
}

.observacoes li {
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

/* Botão WhatsApp Flutuante */
.whatsapp-float {
    position: fixed;
    bottom: 20px;
    right: 180px;
    z-index: 1001;
}

.whatsapp-button {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: #25D366;
    color: var(--branco);
    padding: 1.25rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: bold;
    box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
    transition: all 0.3s ease;
    animation: pulse 2s infinite;
    font-size: 1.1rem;
}

.whatsapp-button:hover {
    background: #128C7E;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.6);
}

.whatsapp-button svg {
    width: 28px;
    height: 28px;
}

.whatsapp-text {
    font-size: 1rem;
}

@keyframes pulse {
    0% {
        box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 8px 25px rgba(37, 211, 102, 0.8);
        transform: scale(1.05);
    }
    100% {
        box-shadow: 0 6px 20px rgba(37, 211, 102, 0.4);
        transform: scale(1);
    }
}

/* Footer */
.footer {
    background: var(--azul-escuro);
    color: var(--branco);
    text-align: center;
    padding: 2rem 0;
}

/* Responsividade Moderna */
@media (max-width: 1024px) {
    .container {
        padding: 0 1.5rem;
    }

    .hero-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 1rem;
        max-width: 900px;
    }

    .stat-card {
        padding: 1.25rem;
        min-height: 130px;
    }

    .stat-icon {
        width: 42px;
        height: 42px;
    }

    .stat-number {
        font-size: 1.6rem;
    }

    .stat-label {
        font-size: 0.85rem;
    }

    .stat-description {
        font-size: 0.7rem;
    }

    .sobre-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .servicos-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    .valores-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    /* Header mobile */
    .header .container {
        padding: 1rem;
    }

    .nav {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .logo-text {
        display: none;
    }

    /* Hero mobile */
    .hero {
        padding: 160px 0 120px; /* Aumentado o padding-bottom para acomodar as 4 cards em 2 linhas */
        min-height: auto; /* Removido min-height fixo para permitir que o conteúdo determine a altura */
        position: relative;
    }

    .hero h1 {
        font-size: clamp(2rem, 8vw, 2.5rem);
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-buttons {
        flex-direction: column;
        width: 100%;
    }

    .btn-primary,
    .btn-secondary {
        width: 100%;
        justify-content: center;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin-top: 3rem;
        margin-bottom: 4rem; /* Adicionar margem inferior para separar das setas de scroll */
    }

    /* Seta indicativa para scroll - mostrar apenas no mobile */
    .scroll-indicator {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        color: var(--white);
        opacity: 0.8;
        animation: bounce 2s infinite;
        cursor: pointer;
        transition: opacity var(--transition-normal);
        z-index: 10; /* Garantir que fique acima de outros elementos */
    }

    .scroll-indicator:hover {
        opacity: 1;
    }

    .scroll-indicator-text {
        font-size: 0.8rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 1px;
    }

    .scroll-arrow {
        width: 24px;
        height: 24px;
        border: 2px solid var(--white);
        border-top: none;
        border-left: none;
        transform: rotate(45deg);
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateX(-50%) translateY(0);
        }
        40% {
            transform: translateX(-50%) translateY(-10px);
        }
        60% {
            transform: translateX(-50%) translateY(-5px);
        }
    }

    .stat-card {
        padding: 1.25rem;
        gap: 0.75rem;
        min-height: 120px;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .stat-description {
        font-size: 0.7rem;
    }

    /* Sections mobile */
    .sobre,
    .servicos {
        padding: 40px 0;
    }

    .sobre-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .servicos-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .sobre-item,
    .servico-item {
        padding: 2rem 1.5rem;
    }

    .valores-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .valor-item {
        padding: 1.5rem 1rem;
    }

    .valor-numero {
        font-size: 2rem;
    }

    /* Serviços mobile */
    .servico-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .servicos-cta {
        padding: 2rem 1.5rem;
    }

    /* Form mobile */
    .form-group {
        flex-direction: column;
        align-items: flex-start;
    }

    .form-group label {
        min-width: auto;
    }

    /* WhatsApp button mobile */
    .whatsapp-float {
        bottom: 15px;
        right: 85px;
    }

    .whatsapp-button {
        padding: 1rem 1.25rem;
        border-radius: 50px;
        font-size: 1rem;
    }

    .whatsapp-text {
        display: none;
    }

    .whatsapp-button svg {
        width: 24px;
        height: 24px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }

    .hero {
        padding: 140px 0 100px; /* Aumentado o padding-bottom para acomodar as 4 cards em coluna única */
    }

    .hero-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-bottom: 3rem; /* Margem inferior para separar das setas de scroll */
    }

    .stat-card {
        padding: 1rem;
        gap: 0.5rem;
        min-height: 100px;
    }

    .stat-icon {
        width: 36px;
        height: 36px;
    }

    .stat-number {
        font-size: 1.4rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    .stat-description {
        font-size: 0.65rem;
    }

    .sobre-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .valores-grid {
        grid-template-columns: 1fr;
    }

    .sobre-item-icon,
    .servico-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .btn-primary,
    .btn-secondary {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    /* Ajustes da seta indicativa para telas menores */
    .scroll-indicator {
        bottom: 15px;
    }

    .scroll-indicator-text {
        font-size: 0.7rem;
    }

    .scroll-arrow {
        width: 20px;
        height: 20px;
    }
}

/* Componentes SVG */
.warning-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--accent-red);
    border-radius: 50%;
    color: var(--white);
    margin-bottom: 1rem;
}

.offer-icon {
    display: inline-flex;
    align-items: center;
    margin-right: 0.5rem;
    color: var(--accent-yellow);
}

.benefit {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.benefit svg {
    color: var(--secondary-green);
    flex-shrink: 0;
}

.urgency-icon {
    display: inline-flex;
    align-items: center;
    color: var(--accent-yellow);
}

.social-proof-avatar {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--primary-blue);
    border-radius: 50%;
    color: var(--white);
}

/* Componentes SVG para formulários */
.service-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    color: var(--white);
    margin-bottom: 0.5rem;
}

/* Componentes SVG para comparação */
.comparison-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.comparison-title.negative svg {
    color: var(--accent-red);
}

.comparison-title.positive svg {
    color: var(--secondary-green);
}

/* Componentes SVG para timeline */
.timeline-time {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--gray-600);
    margin-top: 0.5rem;
}

.timeline-time svg {
    color: var(--primary-blue);
}

/* Loading animation */
body {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
}

body.loaded {
    opacity: 1;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
*:focus {
    outline: 2px solid var(--primary-blue);
    outline-offset: 2px;
}

/* Toggle Options (Radio Buttons Modernos) */
.toggle-group {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
}

.toggle-option {
    position: relative;
}

.toggle-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    pointer-events: none;
}

.toggle-option label {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--gray-50);
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.toggle-option label::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 102, 255, 0.1), transparent);
    transition: left 0.5s;
}

.toggle-option label:hover::before {
    left: 100%;
}

.toggle-option label:hover {
    border-color: var(--primary-blue);
    background: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.toggle-option input[type="radio"]:checked + label {
    background: var(--white);
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.toggle-option input[type="radio"]:checked + label .toggle-icon {
    background: var(--gradient-primary);
    color: var(--white);
    transform: scale(1.1);
}

.toggle-icon {
    width: 50px;
    height: 50px;
    background: var(--gray-200);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    transition: all var(--transition-normal);
    flex-shrink: 0;
}

.toggle-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.toggle-title {
    font-weight: 600;
    color: var(--gray-900);
    font-size: 1rem;
}

.toggle-desc {
    font-size: 0.85rem;
    color: var(--gray-600);
}

/* Navegação do formulário */
.form-navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 2rem;
    gap: 1rem;
}

.btn-prev,
.btn-next {
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.btn-prev {
    background: var(--gray-100);
    color: var(--gray-700);
}

.btn-prev:hover {
    background: var(--gray-200);
    transform: translateY(-2px);
}

.btn-next {
    background: var(--gradient-primary);
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-next:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Redefinindo btn-submit para o novo design */
.contact-form .btn-submit {
    background: var(--gradient-primary);
    color: var(--white);
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-md);
    margin-top: 0;
}

.contact-form .btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: var(--primary-blue-dark);
}

/* Barra de progresso */
.form-progress {
    padding: 2rem 3rem;
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
}

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--gray-200);
    border-radius: 2px;
    margin-bottom: 1rem;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 2px;
    transition: width var(--transition-normal);
    width: 33.33%;
}

.progress-steps {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.step-indicator {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--gray-200);
    color: var(--gray-600);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: all var(--transition-normal);
    position: relative;
}

.step-indicator.active {
    background: var(--gradient-primary);
    color: var(--white);
    transform: scale(1.1);
}

.step-indicator.completed {
    background: var(--secondary-green);
    color: var(--white);
}

/* Footer do contato */
.contato-footer {
    text-align: center;
    margin-top: 3rem;
}

.security-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--white);
    padding: 1rem 1.5rem;
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    color: var(--gray-700);
    font-weight: 500;
    margin-bottom: 1rem;
}

.security-badge svg {
    color: var(--secondary-green);
}

.privacy-text {
    color: var(--gray-600);
    font-size: 0.9rem;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Animações */
@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Responsividade do formulário */
@media (max-width: 768px) {
    .contato-wrapper {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contato-info {
        position: static;
        order: 2;
    }

    .form-row {
        flex-direction: column;
        gap: 1rem;
    }

    .form-step {
        padding: 2rem 1.5rem;
    }

    .form-progress {
        padding: 1.5rem;
    }

    .form-navigation {
        flex-direction: column;
        gap: 1rem;
    }

    .btn-prev,
    .btn-next,
    .contact-form .btn-submit {
        width: 100%;
        justify-content: center;
    }

    .toggle-option label {
        padding: 1rem;
    }

    .toggle-icon {
        width: 40px;
        height: 40px;
    }

    .form-group input,
    .form-group textarea {
        padding: 0.875rem 1rem;
        font-size: 16px; /* Evita zoom no iOS */
    }

    .form-group label {
        font-size: 0.95rem;
    }
}

/* Estilos para páginas internas */
.form-section,
.calculator-section,
.blog-section,
.info-section {
    padding-top: 120px;
    min-height: 100vh;
}

.form-title,
.calculator-title,
.blog-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-top: 1.5rem;
}

.form-title h1,
.calculator-title h1,
.blog-header h1 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.calculator-title p,
.blog-header p {
    font-size: 1.2rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* Estilos para formulários das páginas internas */
.help-form,
.switch-form {
    background: var(--white);
    padding: 3rem;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-100);
    max-width: 800px;
    margin: 0 auto;
}

.help-form .form-group,
.switch-form .form-group {
    margin-bottom: 1.5rem;
}

.help-form .form-group label,
.switch-form .form-group label {
    display: block;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.help-form .form-group input,
.help-form .form-group textarea,
.switch-form .form-group input {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    background: var(--white);
    font-size: 1rem;
    color: var(--gray-900);
    transition: all var(--transition-normal);
    outline: none;
    font-family: inherit;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.help-form .form-group input:focus,
.help-form .form-group textarea:focus,
.switch-form .form-group input:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 102, 255, 0.1);
    transform: translateY(-1px);
}

.help-form .form-group textarea {
    min-height: 120px;
    resize: vertical;
    line-height: 1.6;
}

/* Radio groups para páginas internas */
.radio-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.radio-group.horizontal {
    flex-direction: row;
    gap: 2rem;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-weight: 500;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: background-color var(--transition-normal);
}

.radio-group label:hover {
    background-color: var(--gray-50);
}

.radio-group input[type="radio"] {
    width: auto;
    margin: 0;
    padding: 0;
    box-shadow: none;
    transform: none;
}

/* Botões de submit para páginas internas */
.help-form .btn-submit,
.switch-form .btn-submit {
    background: var(--gradient-primary);
    color: var(--white);
    padding: 1rem 2rem;
    border: none;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all var(--transition-normal);
    width: 100%;
    box-shadow: var(--shadow-md);
    margin-top: 1rem;
}

.help-form .btn-submit:hover,
.switch-form .btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: var(--primary-blue-dark);
}

/* Observações */
.observacoes {
    margin-top: 3rem;
    background: var(--gray-50);
    padding: 2rem;
    border-radius: var(--radius-xl);
    border-left: 4px solid var(--accent-yellow);
}

.observacoes h3 {
    color: var(--gray-900);
    margin-bottom: 1rem;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
}

.observacoes ol {
    color: var(--gray-700);
    line-height: 1.6;
    padding-left: 1.5rem;
}

.observacoes li {
    margin-bottom: 0.5rem;
}

/* Seção de Depoimentos */
.testimonials {
    background: var(--gray-50);
    padding: 60px 0;
    position: relative;
}

.testimonials-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.testimonials-header h2 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.testimonials-subtitle {
    font-size: 1.2rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: 2rem;
    border: 2px solid var(--gray-100);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.testimonial-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.testimonial-card:hover::before {
    transform: scaleX(1);
}

.testimonial-card:hover {
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-colored);
    transform: translateY(-8px);
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.stars {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1rem;
    color: var(--accent-yellow);
}

.testimonial-content p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--gray-700);
    font-style: italic;
    position: relative;
}

.testimonial-content p::before {
    content: '"';
    font-size: 3rem;
    color: var(--primary-blue);
    position: absolute;
    top: -10px;
    left: -20px;
    font-family: serif;
    opacity: 0.3;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-info h4 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
}

.author-info span {
    color: var(--gray-600);
    font-size: 0.9rem;
}

/* Responsividade para depoimentos */
@media (max-width: 768px) {
    .testimonials {
        padding: 40px 0;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .testimonial-card {
        padding: 1.5rem;
    }
}

/* Seção FAQ Interativa */
.faq-section {
    background: var(--white);
    padding: 60px 0;
    position: relative;
}

.faq-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.faq-header h2 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.faq-subtitle {
    font-size: 1.2rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto 2rem;
    line-height: 1.6;
}

.faq-search {
    max-width: 500px;
    margin: 0 auto;
}

.search-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-container svg {
    position: absolute;
    left: 1rem;
    color: var(--gray-400);
    z-index: 2;
}

.search-container input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-xl);
    background: var(--white);
    font-size: 1rem;
    color: var(--gray-900);
    transition: all var(--transition-normal);
    outline: none;
    box-shadow: var(--shadow-sm);
}

.search-container input:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 90, 236, 0.1);
    transform: translateY(-2px);
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
    margin-top: 3rem;
}

.faq-item {
    background: var(--white);
    border: 2px solid var(--gray-100);
    border-radius: var(--radius-xl);
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.faq-item:hover {
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-colored);
    transform: translateY(-2px);
}

.faq-item.active {
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-colored);
}

.faq-question {
    padding: 1.5rem 2rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--white);
    transition: all var(--transition-normal);
}

.faq-question:hover {
    background: var(--gray-50);
}

.faq-question h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    flex: 1;
    line-height: 1.4;
}

.faq-icon {
    color: var(--primary-blue);
    transition: transform var(--transition-normal);
    flex-shrink: 0;
    margin-left: 1rem;
}

.faq-item.active .faq-icon {
    transform: rotate(45deg);
}

.faq-answer {
    background: var(--gray-50);
    border-top: 1px solid var(--gray-200);
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease-out, padding 0.4s ease-out;
    padding: 0 2rem;
}

.faq-item.active .faq-answer {
    max-height: 500px;
    padding: 1.5rem 2rem;
}

.faq-answer p {
    color: var(--gray-700);
    line-height: 1.7;
    margin: 0;
    font-size: 1rem;
}

.faq-answer strong {
    color: var(--gray-900);
    font-weight: 600;
}

.faq-answer a {
    color: var(--primary-blue);
    text-decoration: none;
    font-weight: 600;
    transition: color var(--transition-normal);
}

.faq-answer a:hover {
    color: var(--primary-blue-dark);
    text-decoration: underline;
}

/* Animação para o FAQ */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsividade FAQ */
@media (max-width: 768px) {
    .faq-section {
        padding: 60px 0;
    }

    .faq-question {
        padding: 1.25rem 1.5rem;
    }

    .faq-question h3 {
        font-size: 1.1rem;
    }

    .faq-answer {
        padding: 0 1.5rem 1.25rem;
    }

    .search-container input {
        padding: 0.875rem 0.875rem 0.875rem 2.75rem;
        font-size: 16px; /* Evita zoom no iOS */
    }
}

/* Página Como Podemos Te Ajudar - Estilos Modernos */

/* Hero da página de ajuda */
.help-hero {
    background: var(--gradient-hero);
    color: var(--white);
    padding: 140px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.help-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 70%, rgba(1, 216, 0, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 70% 30%, rgba(255, 226, 6, 0.2) 0%, transparent 50%);
    animation: float 15s ease-in-out infinite;
}

.help-hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
}

.help-hero h1 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.help-hero-subtitle {
    font-size: clamp(1.1rem, 2.5vw, 1.3rem);
    margin-bottom: 2.5rem;
    opacity: 0.95;
    line-height: 1.6;
}

.help-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.help-stat-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: var(--radius-xl);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
    min-width: 150px;
}

.help-stat-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-4px);
}

.help-stat-number {
    display: block;
    font-family: 'Poppins', sans-serif;
    font-size: 2rem;
    font-weight: 800;
    color: var(--accent-yellow);
    margin-bottom: 0.5rem;
}

.help-stat-label {
    font-size: 0.9rem;
    font-weight: 600;
    opacity: 0.9;
}

/* Seção Missão */
.mission-section {
    background: var(--gray-50);
    padding: 60px 0;
}

.mission-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.mission-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: 2.5rem;
    text-align: center;
    border: 2px solid var(--gray-100);
    transition: all var(--transition-normal);
}

.mission-card:hover {
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-colored);
    transform: translateY(-8px);
}

.mission-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    margin: 0 auto 1.5rem;
    transition: all var(--transition-normal);
}

.mission-card:hover .mission-icon {
    background: var(--gradient-secondary);
    transform: scale(1.1);
}

.mission-card h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.mission-card p {
    color: var(--gray-700);
    line-height: 1.6;
}

/* Seção de Áreas */
.areas-section {
    background: var(--white);
    padding: 60px 0;
}

.areas-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.areas-header h2 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.areas-subtitle {
    font-size: 1.2rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.areas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.area-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: 2rem;
    border: 2px solid var(--gray-100);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.area-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.area-card:hover::before {
    transform: scaleX(1);
}

.area-card:hover {
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-colored);
    transform: translateY(-8px);
}

.area-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-accent);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-900);
    margin-bottom: 1.5rem;
    transition: all var(--transition-normal);
}

.area-card:hover .area-icon {
    background: var(--gradient-primary);
    color: var(--white);
    transform: scale(1.1);
}

.area-card h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.area-card p {
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.area-benefits {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.benefit {
    font-size: 0.9rem;
    color: var(--secondary-green);
    font-weight: 600;
}

/* CTA da página de ajuda */
.help-cta-section {
    background: var(--gradient-hero);
    color: var(--white);
    padding: 50px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.help-cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
}

.help-cta-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
}

.help-cta-content h2 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 2.5rem);
    font-weight: 700;
    margin-bottom: 1rem;
}

.help-cta-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.95;
    line-height: 1.6;
}

.help-cta-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2.5rem;
    flex-wrap: wrap;
}

.help-cta-stat {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
    border-radius: var(--radius-lg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 120px;
}

.help-cta-stat-number {
    display: block;
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--accent-yellow);
    margin-bottom: 0.25rem;
}

.help-cta-stat-label {
    font-size: 0.85rem;
    font-weight: 600;
    opacity: 0.9;
}

.help-cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Formulário da página de ajuda */
.form-header {
    text-align: center;
    margin-bottom: 3rem;
}

.form-header h2 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 2.5rem);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.form-subtitle {
    font-size: 1.1rem;
    color: var(--gray-600);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Formulário da página de ajuda - Layout vertical melhorado */
.help-form .form-group {
    margin-bottom: 2rem;
}

.help-form .form-group:last-of-type {
    margin-bottom: 1.5rem;
}

.help-form .form-group input[type="text"],
.help-form .form-group input[type="email"],
.help-form .form-group input[type="tel"],
.help-form .form-group textarea {
    width: 100%;
    padding: 1rem 1.25rem;
    font-size: 1rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    background: var(--white);
    transition: all var(--transition-normal);
    font-family: inherit;
    line-height: 1.5;
}

.help-form .form-group input[type="text"]:focus,
.help-form .form-group input[type="email"]:focus,
.help-form .form-group input[type="tel"]:focus,
.help-form .form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 90, 236, 0.1);
    background: var(--gray-50);
}

.help-form .form-group label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.help-form .form-group label svg {
    color: var(--primary-blue);
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.help-form .form-group textarea {
    min-height: 120px;
    resize: vertical;
}

.help-form .btn-submit {
    width: 100%;
    padding: 1.25rem 2rem;
    font-size: 1.1rem;
    font-weight: 700;
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-top: 1rem;
    box-shadow: var(--shadow-md);
}

.help-form .btn-submit:hover {
    background: var(--gradient-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.help-form .btn-submit:active {
    transform: translateY(0);
}

.help-form .btn-submit svg {
    transition: transform var(--transition-normal);
}

.help-form .btn-submit:hover svg {
    transform: translateX(4px);
}

/* Formulário da página Trocar de Contabilidade - Layout vertical melhorado */
.switch-form .form-group {
    margin-bottom: 2rem;
}

.switch-form .form-group:last-of-type {
    margin-bottom: 1.5rem;
}

.switch-form .form-group input[type="text"],
.switch-form .form-group input[type="email"],
.switch-form .form-group input[type="tel"],
.switch-form .form-group input[type="number"] {
    width: 100%;
    padding: 1rem 1.25rem;
    font-size: 1rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    background: var(--white);
    transition: all var(--transition-normal);
    font-family: inherit;
    line-height: 1.5;
}

.switch-form .form-group input[type="text"]:focus,
.switch-form .form-group input[type="email"]:focus,
.switch-form .form-group input[type="tel"]:focus,
.switch-form .form-group input[type="number"]:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 90, 236, 0.1);
    background: var(--gray-50);
}

.switch-form .form-group label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.switch-form .form-group label svg {
    color: var(--primary-blue);
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.switch-form .btn-submit {
    width: 100%;
    padding: 1.25rem 2rem;
    font-size: 1.1rem;
    font-weight: 700;
    background: var(--gradient-primary);
    color: var(--white);
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-top: 1rem;
    box-shadow: var(--shadow-md);
}

.switch-form .btn-submit:hover {
    background: var(--gradient-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.switch-form .btn-submit:active {
    transform: translateY(0);
}

.switch-form .btn-submit svg {
    transition: transform var(--transition-normal);
}

.switch-form .btn-submit:hover svg {
    transform: translateX(4px);
}

/* Radio buttons melhorados para a página de troca */
.switch-form .radio-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
}

.switch-form .radio-option label {
    padding: 1.25rem 1.5rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-lg);
    background: var(--white);
    cursor: pointer;
    transition: all var(--transition-normal);
    margin-bottom: 0 !important;
    font-size: 1rem;
}

.switch-form .radio-option label:hover {
    border-color: var(--primary-blue);
    background: var(--gray-50);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.switch-form .radio-option input[type="radio"]:checked + label {
    border-color: var(--primary-blue);
    background: rgba(0, 90, 236, 0.05);
    box-shadow: var(--shadow-colored);
}

.switch-form .radio-icon {
    width: 32px;
    height: 32px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 800;
    transition: all var(--transition-normal);
    flex-shrink: 0;
}

.switch-form .radio-option input[type="radio"]:checked + label .radio-icon {
    background: var(--gradient-primary);
    color: var(--white);
    transform: scale(1.1);
}

.switch-form .cnpj-label,
.switch-form .funcionarios-label {
    display: flex !important;
    align-items: center;
    gap: 0.75rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.switch-form .conditional {
    margin-top: 1rem;
    padding: 1.5rem;
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    border: 2px solid var(--gray-100);
    transition: all var(--transition-normal);
}

.switch-form .conditional.show {
    border-color: var(--primary-blue);
    background: rgba(0, 90, 236, 0.02);
}

.service-label {
    display: block !important;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.service-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.service-option {
    position: relative;
}

.service-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.service-option label {
    display: flex !important;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-xl);
    background: var(--white);
    cursor: pointer;
    transition: all var(--transition-normal);
    margin-bottom: 0 !important;
}

.service-option label:hover {
    border-color: var(--primary-blue);
    background: var(--gray-50);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.service-option input[type="radio"]:checked + label {
    border-color: var(--primary-blue);
    background: rgba(0, 90, 236, 0.05);
    box-shadow: var(--shadow-colored);
}

.service-icon {
    width: 32px;
    height: 32px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    transition: all var(--transition-normal);
    flex-shrink: 0;
}

.service-option input[type="radio"]:checked + label .service-icon {
    background: var(--gradient-primary);
    color: var(--white);
    transform: scale(1.1);
}

.textarea-note {
    color: var(--gray-600);
    font-size: 0.85rem;
    margin-top: 0.5rem;
    font-style: italic;
}

/* Responsividade da página de ajuda */
@media (max-width: 768px) {
    .help-hero {
        padding: 120px 0 60px;
    }

    .help-stats {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .help-stat-item {
        width: 100%;
        max-width: 200px;
    }

    .mission-section,
    .areas-section {
        padding: 60px 0;
    }

    .mission-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .mission-card {
        padding: 2rem;
    }

    .areas-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .area-card {
        padding: 1.5rem;
    }

    .help-cta-section {
        padding: 60px 0;
    }

    .help-cta-stats {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .help-cta-stat {
        width: 100%;
        max-width: 150px;
    }

    .help-cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .help-cta-buttons .btn-primary,
    .help-cta-buttons .btn-secondary {
        width: 100%;
        max-width: 280px;
    }

    .help-form .form-group {
        margin-bottom: 1.5rem;
    }

    .help-form .form-group input[type="text"],
    .help-form .form-group input[type="email"],
    .help-form .form-group input[type="tel"],
    .help-form .form-group textarea {
        padding: 0.875rem 1rem;
        font-size: 0.95rem;
    }

    .help-form .form-group label {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .help-form .btn-submit {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    /* Responsividade para formulário da página de troca */
    .switch-form .form-group {
        margin-bottom: 1.5rem;
    }

    .switch-form .form-group input[type="text"],
    .switch-form .form-group input[type="email"],
    .switch-form .form-group input[type="tel"],
    .switch-form .form-group input[type="number"] {
        padding: 0.875rem 1rem;
        font-size: 0.95rem;
    }

    .switch-form .form-group label {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }

    .switch-form .btn-submit {
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    .switch-form .radio-option label {
        padding: 1rem 1.25rem;
        font-size: 0.95rem;
    }

    .switch-form .conditional {
        padding: 1.25rem;
        margin-top: 0.75rem;
    }

    .service-options {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .service-option label {
        padding: 1rem;
        gap: 0.75rem;
    }
}

/* Página Trocar de Contabilidade - Estilos Modernos */

/* Hero da página de troca */
.switch-hero {
    background: var(--gradient-primary);
    color: var(--white);
    padding: 140px 0 80px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.switch-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.switch-hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    margin: 0 auto;
}

.switch-hero h1 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.switch-hero-subtitle {
    font-size: clamp(1.1rem, 2.5vw, 1.3rem);
    margin-bottom: 2.5rem;
    opacity: 0.95;
    line-height: 1.6;
}

.switch-benefits-preview {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
    margin-top: 2rem;
}

.benefit-preview {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-full);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-normal);
}

.benefit-preview:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.benefit-preview svg {
    color: var(--accent-yellow);
}

.benefit-preview span {
    font-weight: 600;
    font-size: 0.9rem;
}

/* Seção Por Que Trocar */
.why-switch-section {
    background: var(--gray-50);
    padding: 60px 0;
    position: relative;
}

.why-switch-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.why-switch-header h2 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.why-switch-subtitle {
    font-size: 1.2rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.comparison-section {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: start;
    max-width: 1000px;
    margin: 0 auto;
}

.comparison-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: 2.5rem;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
}

.comparison-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.comparison-title {
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-align: center;
}

.comparison-title.negative {
    color: var(--accent-red);
}

.comparison-title.positive {
    color: var(--secondary-green);
}

.comparison-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.comparison-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-100);
    color: var(--gray-700);
    line-height: 1.5;
}

.comparison-list li:last-child {
    border-bottom: none;
}

.vs-divider {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    color: var(--white);
    font-weight: 800;
    font-size: 1.5rem;
    margin-top: 2rem;
    box-shadow: var(--shadow-lg);
}

/* Seção de Benefícios Modernizada */
.benefits-section {
    background: var(--white);
    padding: 60px 0;
}

.benefits-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.benefits-header h2 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.benefits-subtitle {
    font-size: 1.2rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.benefits-grid-modern {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.benefit-card {
    background: var(--white);
    border-radius: var(--radius-2xl);
    padding: 2.5rem;
    border: 2px solid var(--gray-100);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.benefit-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.benefit-card:hover::before {
    transform: scaleX(1);
}

.benefit-card:hover {
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-colored);
    transform: translateY(-8px);
}

.benefit-icon {
    width: 70px;
    height: 70px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    margin-bottom: 1.5rem;
    transition: all var(--transition-normal);
}

.benefit-card:hover .benefit-icon {
    background: var(--gradient-secondary);
    transform: scale(1.1);
}

.benefit-card h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.benefit-card p {
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.benefit-highlight {
    background: var(--gradient-accent);
    color: var(--gray-900);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-lg);
    font-weight: 600;
    font-size: 0.9rem;
    text-align: center;
    box-shadow: var(--shadow-sm);
}

/* Seção Processo de Migração */
.process-section {
    background: var(--gray-50);
    padding: 60px 0;
}

.process-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.process-header h2 {
    font-family: 'Poppins', sans-serif;
    font-size: clamp(2rem, 4vw, 2.5rem);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.process-subtitle {
    font-size: 1.2rem;
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

.process-timeline {
    max-width: 800px;
    margin: 0 auto 3rem;
    position: relative;
}

.process-timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--gradient-primary);
    transform: translateX(-50%);
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 3rem;
    position: relative;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-item:nth-child(even) .timeline-content {
    text-align: right;
}

.timeline-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 2;
    box-shadow: var(--shadow-lg);
    margin: 0 2rem;
}

.step-number {
    font-family: 'Poppins', sans-serif;
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--white);
}

.timeline-content {
    flex: 1;
    background: var(--white);
    padding: 2rem;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.timeline-content:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
}

.timeline-content h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.75rem;
}

.timeline-content p {
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: 1rem;
}

.timeline-time {
    background: var(--gradient-accent);
    color: var(--gray-900);
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: 0.85rem;
    font-weight: 600;
    display: inline-block;
}

.process-guarantee {
    text-align: center;
}

.guarantee-card {
    background: var(--white);
    border: 2px solid var(--secondary-green);
    border-radius: var(--radius-2xl);
    padding: 2.5rem;
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

.guarantee-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-secondary);
}

.guarantee-card h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.guarantee-card p {
    color: var(--gray-700);
    line-height: 1.6;
    margin: 0;
}

/* Formulário da página de troca */
.radio-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.radio-option {
    position: relative;
}

.radio-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.radio-option label {
    display: flex !important;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem;
    border: 2px solid var(--gray-200);
    border-radius: var(--radius-xl);
    background: var(--white);
    cursor: pointer;
    transition: all var(--transition-normal);
    margin-bottom: 0 !important;
}

.radio-option label:hover {
    border-color: var(--primary-blue);
    background: var(--gray-50);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.radio-option input[type="radio"]:checked + label {
    border-color: var(--primary-blue);
    background: rgba(0, 90, 236, 0.05);
    box-shadow: var(--shadow-colored);
}

.radio-icon {
    width: 32px;
    height: 32px;
    background: var(--gray-200);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 800;
    transition: all var(--transition-normal);
    flex-shrink: 0;
}

.radio-option input[type="radio"]:checked + label .radio-icon {
    background: var(--gradient-primary);
    color: var(--white);
    transform: scale(1.1);
}

.cnpj-label,
.funcionarios-label {
    display: block !important;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

/* Responsividade da página de troca */
@media (max-width: 768px) {
    .switch-hero {
        padding: 120px 0 60px;
    }

    .switch-benefits-preview {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .benefit-preview {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }

    .why-switch-section,
    .benefits-section,
    .process-section {
        padding: 60px 0;
    }

    .comparison-section {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .vs-divider {
        margin: 1rem auto;
        width: 60px;
        height: 60px;
        font-size: 1.2rem;
    }

    .comparison-card {
        padding: 2rem;
    }

    .benefits-grid-modern {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .benefit-card {
        padding: 2rem;
    }

    .process-timeline::before {
        left: 40px;
    }

    .timeline-item {
        flex-direction: row !important;
        padding-left: 100px;
    }

    .timeline-item:nth-child(even) .timeline-content {
        text-align: left;
    }

    .timeline-icon {
        position: absolute;
        left: 0;
        margin: 0;
        width: 60px;
        height: 60px;
    }

    .step-number {
        font-size: 1.2rem;
    }

    .timeline-content {
        padding: 1.5rem;
    }

    .guarantee-card {
        padding: 2rem;
    }

    .radio-options {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .radio-option label {
        padding: 1rem;
        gap: 0.75rem;
    }
}

/* Print styles */
@media print {
    .header,
    .whatsapp-float,
    .scroll-indicator,
    .testimonials {
        display: none;
    }

    .hero {
        background: none;
        color: black;
    }

    * {
        box-shadow: none !important;
    }
}
