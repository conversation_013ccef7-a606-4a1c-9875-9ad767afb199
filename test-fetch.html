<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Fetch</title>
</head>
<body>
    <h1>Teste de Carregamento de Arquivos Markdown</h1>
    <button onclick="testFetch()">Testar Fetch</button>
    <div id="result"></div>

    <script>
        async function testFetch() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Carregando...';
            
            try {
                console.log('Tentando carregar arquivo...');
                const response = await fetch('conteudo/post-1-como-abrir-empresa-2024.md');
                
                console.log('Response status:', response.status);
                console.log('Response ok:', response.ok);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const content = await response.text();
                console.log('Conte<PERSON><PERSON> carregado:', content.substring(0, 100) + '...');
                
                resultDiv.innerHTML = `
                    <h2>Sucesso!</h2>
                    <p>Status: ${response.status}</p>
                    <p>Tamanho: ${content.length} caracteres</p>
                    <h3>Primeiras linhas:</h3>
                    <pre>${content.substring(0, 500)}...</pre>
                `;
            } catch (error) {
                console.error('Erro:', error);
                resultDiv.innerHTML = `
                    <h2>Erro!</h2>
                    <p style="color: red;">${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
