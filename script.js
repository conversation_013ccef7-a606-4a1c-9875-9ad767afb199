// Funcionalidades gerais do site
document.addEventListener('DOMContentLoaded', function() {

    // Registrar Service Worker para PWA
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            navigator.serviceWorker.register('/sw.js')
                .then(function(registration) {
                    console.log('ServiceWorker registrado com sucesso: ', registration.scope);
                })
                .catch(function(error) {
                    console.log('Falha ao registrar ServiceWorker: ', error);
                });
        });
    }

    // Smooth scrolling para links internos
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Formulário de contato
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Validação personalizada
            if (validateContactForm()) {
                // Aqui você pode adicionar a lógica para enviar o formulário
                // Por exemplo, enviar para um servidor ou integrar com WhatsApp
                handleFormSubmission();
            }
        });

        // Validação em tempo real para o campo "Outros"
        const radioButtons = document.querySelectorAll('input[name="opcao"]');
        const mensagemField = document.getElementById('mensagem');

        radioButtons.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'outros') {
                    mensagemField.setAttribute('required', 'required');
                    mensagemField.style.borderColor = '#fd0e35';
                    mensagemField.placeholder = 'Este campo é obrigatório quando "Outros" é selecionado';
                } else {
                    mensagemField.removeAttribute('required');
                    mensagemField.style.borderColor = '#ddd';
                    mensagemField.placeholder = 'Escreva Como podemos Ajudar:';
                }
            });
        });
    }

    // Destacar campos obrigatórios
    highlightRequiredFields();
});

function validateContactForm() {
    const form = document.getElementById('contactForm');
    const formData = new FormData(form);
    let isValid = true;
    let errors = [];

    // Validar campos obrigatórios
    const requiredFields = ['nome', 'whatsapp', 'email', 'cidade', 'opcao'];

    requiredFields.forEach(field => {
        const value = formData.get(field);
        if (!value || value.trim() === '') {
            isValid = false;
            errors.push(`O campo ${getFieldLabel(field)} é obrigatório.`);
            highlightErrorField(field);
        }
    });

    // Validação específica para "Outros"
    const opcaoSelecionada = formData.get('opcao');
    const mensagem = formData.get('mensagem');

    if (opcaoSelecionada === 'outros' && (!mensagem || mensagem.trim() === '')) {
        isValid = false;
        errors.push('Quando "Outros" é selecionado, o campo de mensagem é obrigatório.');
        highlightErrorField('mensagem');
    }

    // Validação de email
    const email = formData.get('email');
    if (email && !isValidEmail(email)) {
        isValid = false;
        errors.push('Por favor, insira um email válido.');
        highlightErrorField('email');
    }

    // Validação de WhatsApp (formato básico)
    const whatsapp = formData.get('whatsapp');
    if (whatsapp && !isValidWhatsApp(whatsapp)) {
        isValid = false;
        errors.push('Por favor, insira um número de WhatsApp válido.');
        highlightErrorField('whatsapp');
    }

    if (!isValid) {
        showErrors(errors);
    }

    return isValid;
}

function getFieldLabel(fieldName) {
    const labels = {
        'nome': 'Nome',
        'whatsapp': 'Número WhatsApp',
        'email': 'Email',
        'cidade': 'Cidade',
        'opcao': 'Opção'
    };
    return labels[fieldName] || fieldName;
}

function highlightErrorField(fieldName) {
    const field = document.getElementById(fieldName) || document.querySelector(`input[name="${fieldName}"]`);
    if (field) {
        field.style.borderColor = '#fd0e35';
        field.style.boxShadow = '0 0 5px rgba(253, 14, 53, 0.3)';

        // Remover destaque após correção
        field.addEventListener('input', function() {
            this.style.borderColor = '#ddd';
            this.style.boxShadow = 'none';
        }, { once: true });
    }
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidWhatsApp(whatsapp) {
    // Remove caracteres não numéricos
    const cleanNumber = whatsapp.replace(/\D/g, '');
    // Verifica se tem pelo menos 10 dígitos (formato brasileiro)
    return cleanNumber.length >= 10 && cleanNumber.length <= 15;
}

function showErrors(errors) {
    // Remove alertas anteriores
    const existingAlert = document.querySelector('.error-alert');
    if (existingAlert) {
        existingAlert.remove();
    }

    // Cria novo alerta
    const alertDiv = document.createElement('div');
    alertDiv.className = 'error-alert';
    alertDiv.style.cssText = `
        background: #fd0e35;
        color: white;
        padding: 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
        position: fixed;
        top: 100px;
        right: 20px;
        max-width: 400px;
        z-index: 1001;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    `;

    alertDiv.innerHTML = `
        <strong>Erro no formulário:</strong><br>
        ${errors.join('<br>')}
        <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; color: white; font-size: 1.2rem; cursor: pointer;">&times;</button>
    `;

    document.body.appendChild(alertDiv);

    // Remove automaticamente após 5 segundos
    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 5000);
}

function handleFormSubmission() {
    const form = document.getElementById('contactForm');
    const formData = new FormData(form);

    // Coleta os dados do formulário
    const dados = {
        nome: formData.get('nome'),
        whatsapp: formData.get('whatsapp'),
        email: formData.get('email'),
        cidade: formData.get('cidade'),
        opcao: formData.get('opcao'),
        mensagem: formData.get('mensagem') || ''
    };

    // Cria mensagem para WhatsApp
    const mensagemWhatsApp = criarMensagemWhatsApp(dados);

    // Mostra sucesso
    showSuccessMessage();

    // Limpa o formulário
    form.reset();

    // Abrir WhatsApp
    window.open(`https://wa.me/5541987427111?text=${encodeURIComponent(mensagemWhatsApp)}`, '_blank');

    console.log('Dados do formulário:', dados);
    console.log('Mensagem WhatsApp:', mensagemWhatsApp);
}

function criarMensagemWhatsApp(dados) {
    return `*Novo contato - Logyc Contabilidade*

*Nome:* ${dados.nome}
*WhatsApp:* ${dados.whatsapp}
*Email:* ${dados.email}
*Cidade:* ${dados.cidade}
*Serviço:* ${dados.opcao.replace('-', ' ').toUpperCase()}
*Mensagem:* ${dados.mensagem}

_Enviado através do site da Logyc Contabilidade_`;
}

function showSuccessMessage() {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-alert';
    successDiv.style.cssText = `
        background: #01d800;
        color: white;
        padding: 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
        position: fixed;
        top: 100px;
        right: 20px;
        max-width: 400px;
        z-index: 1001;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    `;

    successDiv.innerHTML = `
        <strong>Sucesso!</strong><br>
        Sua mensagem foi enviada com sucesso. Entraremos em contato em breve!
        <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; color: white; font-size: 1.2rem; cursor: pointer;">&times;</button>
    `;

    document.body.appendChild(successDiv);

    setTimeout(() => {
        if (successDiv.parentElement) {
            successDiv.remove();
        }
    }, 5000);
}

function highlightRequiredFields() {
    // Adiciona asterisco vermelho aos campos obrigatórios
    const requiredLabels = document.querySelectorAll('label');
    requiredLabels.forEach(label => {
        if (label.textContent.includes('*')) {
            label.innerHTML = label.innerHTML.replace('*', '<span style="color: #fd0e35;">*</span>');
        }
    });
}
