// Sistema de Otimização de Conversão - Logyc Contabilidade
class ConversionOptimizer {
    constructor() {
        this.userBehavior = {};
        this.conversionTriggers = [];
        this.exitIntentShown = false;
        this.scrollBasedShown = false;
        this.timeBasedShown = false;
        this.init();
    }

    init() {
        this.setupBehaviorTracking();
        this.setupExitIntent();
        this.setupScrollBasedTriggers();
        this.setupTimeBasedTriggers();
        this.setupUrgencyIndicators();
        this.setupSocialProof();
        this.setupPersonalization();
        this.setupRetargeting();
    }

    setupBehaviorTracking() {
        // Track user engagement level
        let mouseMovements = 0;
        let clicks = 0;
        let scrolls = 0;
        let timeSpent = 0;

        document.addEventListener('mousemove', () => {
            mouseMovements++;
        });

        document.addEventListener('click', () => {
            clicks++;
        });

        document.addEventListener('scroll', () => {
            scrolls++;
        });

        setInterval(() => {
            timeSpent += 1000;
            this.userBehavior = {
                mouseMovements,
                clicks,
                scrolls,
                timeSpent,
                engagementScore: this.calculateEngagementScore(mouseMovements, clicks, scrolls, timeSpent)
            };
        }, 1000);
    }

    calculateEngagementScore(movements, clicks, scrolls, time) {
        // Calculate engagement score (0-100)
        const movementScore = Math.min(movements / 100, 1) * 25;
        const clickScore = Math.min(clicks / 5, 1) * 25;
        const scrollScore = Math.min(scrolls / 10, 1) * 25;
        const timeScore = Math.min(time / 60000, 1) * 25; // 1 minute = max time score

        return Math.round(movementScore + clickScore + scrollScore + timeScore);
    }

    setupExitIntent() {
        let exitIntentTriggered = false;

        document.addEventListener('mouseleave', (e) => {
            if (e.clientY <= 0 && !exitIntentTriggered && !this.exitIntentShown) {
                exitIntentTriggered = true;
                this.exitIntentShown = true;

                setTimeout(() => {
                    this.showExitIntentModal();
                }, 100);
            }
        });

        // Reset trigger after some time
        setTimeout(() => {
            exitIntentTriggered = false;
        }, 5000);
    }

    showExitIntentModal() {
        const modal = this.createModal({
            title: 'Não vá embora ainda!',
            content: `
                <div class="exit-intent-content">
                    <div class="warning-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
                        </svg>
                    </div>
                    <h3>Antes de sair, que tal descobrir quanto você pode economizar?</h3>
                    <p>Nossa calculadora mostra exatamente quanto você pagaria na Logyc Contabilidade.</p>
                    <div class="exit-intent-benefits">
                        <div class="benefit">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                            Cálculo gratuito e instantâneo
                        </div>
                        <div class="benefit">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                            Sem compromisso
                        </div>
                        <div class="benefit">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                            Preços transparentes
                        </div>
                    </div>
                    <div class="exit-intent-actions">
                        <a href="calculadora.html" class="btn-primary">Usar Calculadora Grátis</a>
                        <button class="btn-secondary" onclick="this.closest('.conversion-modal').remove()">Talvez depois</button>
                    </div>
                </div>
            `,
            type: 'exit-intent'
        });

        this.trackConversionEvent('exit_intent_shown');
    }

    setupScrollBasedTriggers() {
        let scrollTrigger75 = false;
        let scrollTrigger90 = false;

        window.addEventListener('scroll', () => {
            const scrollPercent = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;

            // 75% scroll trigger
            if (scrollPercent >= 75 && !scrollTrigger75 && !this.scrollBasedShown) {
                scrollTrigger75 = true;
                this.showScrollBasedOffer();
            }

            // 90% scroll trigger for different offer
            if (scrollPercent >= 90 && !scrollTrigger90) {
                scrollTrigger90 = true;
                this.showBottomPageOffer();
            }
        });
    }

    showScrollBasedOffer() {
        this.scrollBasedShown = true;

        const floatingOffer = document.createElement('div');
        floatingOffer.className = 'floating-conversion-offer';
        floatingOffer.innerHTML = `
            <div class="floating-offer-content">
                <div class="offer-text">
                    <div class="offer-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </div>
                    <strong>Interessado em nossos serviços?</strong>
                    <span>Fale conosco agora e ganhe 10% de desconto no primeiro mês!</span>
                </div>
                <div class="offer-actions">
                    <a href="${this.getWhatsAppURL('Olá! Vi a oferta no site e gostaria de saber mais sobre o desconto de 10%.')}"
                       target="_blank" class="btn-offer">Quero o Desconto</a>
                    <button class="btn-close" onclick="this.parentElement.parentElement.parentElement.remove()">×</button>
                </div>
            </div>
        `;

        document.body.appendChild(floatingOffer);

        // Auto-remove after 15 seconds
        setTimeout(() => {
            if (floatingOffer.parentElement) {
                floatingOffer.remove();
            }
        }, 15000);

        this.trackConversionEvent('scroll_based_offer_shown');
    }

    showBottomPageOffer() {
        // Show modern sticky bottom bar
        const stickyBar = document.createElement('div');
        stickyBar.className = 'sticky-conversion-bar';
        stickyBar.innerHTML = `
            <div class="sticky-bar-content">
                <div class="sticky-bar-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12,2L13.09,8.26L22,9L14.5,15.74L17,22L12,18.5L7,22L9.5,15.74L2,9L10.91,8.26L12,2Z"/>
                    </svg>
                </div>
                <div class="sticky-bar-text">
                    <span class="sticky-title">Transforme sua empresa hoje!</span>
                    <span class="sticky-subtitle">Junte-se a mais de 500 empresas que confiam na Logyc</span>
                </div>
                <div class="sticky-bar-actions">
                    <a href="como-podemos-ajudar.html" class="btn-sticky-primary">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M13.025 1l-2.847 2.828 6.176 6.176h-16.354v3.992h16.354l-6.176 6.176 2.847 2.828 10.975-11z"/>
                        </svg>
                        Começar Agora
                    </a>
                    <a href="calculadora.html" class="btn-sticky-secondary">Calcular Preço</a>
                </div>
                <button class="btn-close-sticky" onclick="this.parentElement.parentElement.remove()" title="Fechar">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                    </svg>
                </button>
            </div>
        `;

        // Add modern styles
        this.addStickyBarStyles();

        document.body.appendChild(stickyBar);

        // Add entrance animation
        setTimeout(() => {
            stickyBar.classList.add('show');
        }, 100);

        this.trackConversionEvent('bottom_page_offer_shown');
    }

    addStickyBarStyles() {
        if (document.getElementById('sticky-bar-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'sticky-bar-styles';
        styles.textContent = `
            .sticky-conversion-bar {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: linear-gradient(135deg, #0066ff 0%, #0052cc 100%);
                color: white;
                z-index: 10000;
                box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
                transform: translateY(100%);
                transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                backdrop-filter: blur(10px);
                border-top: 1px solid rgba(255, 255, 255, 0.1);
            }

            .sticky-conversion-bar.show {
                transform: translateY(0);
            }

            .sticky-bar-content {
                display: flex;
                align-items: center;
                gap: 1rem;
                padding: 1rem 1.5rem;
                max-width: 1200px;
                margin: 0 auto;
                position: relative;
            }

            .sticky-bar-icon {
                width: 40px;
                height: 40px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-shrink: 0;
                animation: pulse-glow 2s infinite;
            }

            @keyframes pulse-glow {
                0%, 100% {
                    transform: scale(1);
                    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.4);
                }
                50% {
                    transform: scale(1.05);
                    box-shadow: 0 0 0 8px rgba(255, 255, 255, 0);
                }
            }

            .sticky-bar-text {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 0.25rem;
            }

            .sticky-title {
                font-weight: 700;
                font-size: 1.1rem;
                line-height: 1.2;
            }

            .sticky-subtitle {
                font-size: 0.9rem;
                opacity: 0.9;
                line-height: 1.3;
            }

            .sticky-bar-actions {
                display: flex;
                gap: 0.75rem;
                align-items: center;
            }

            .btn-sticky-primary {
                background: rgba(255, 255, 255, 0.2);
                color: white;
                padding: 0.75rem 1.5rem;
                border-radius: 8px;
                text-decoration: none;
                font-weight: 600;
                font-size: 0.9rem;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
                transition: all 0.3s ease;
                border: 1px solid rgba(255, 255, 255, 0.3);
                backdrop-filter: blur(10px);
            }

            .btn-sticky-primary:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            }

            .btn-sticky-secondary {
                background: transparent;
                color: white;
                padding: 0.75rem 1rem;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                text-decoration: none;
                font-weight: 500;
                font-size: 0.85rem;
                transition: all 0.3s ease;
            }

            .btn-sticky-secondary:hover {
                background: rgba(255, 255, 255, 0.1);
                border-color: rgba(255, 255, 255, 0.5);
            }

            .btn-close-sticky {
                background: none;
                border: none;
                color: white;
                cursor: pointer;
                padding: 0.5rem;
                border-radius: 4px;
                transition: all 0.3s ease;
                opacity: 0.7;
                margin-left: 0.5rem;
            }

            .btn-close-sticky:hover {
                background: rgba(255, 255, 255, 0.1);
                opacity: 1;
            }

            /* Mobile responsiveness */
            @media (max-width: 768px) {
                .sticky-bar-content {
                    flex-direction: column;
                    gap: 0.75rem;
                    padding: 1rem;
                    text-align: center;
                }

                .sticky-bar-text {
                    order: 1;
                }

                .sticky-bar-icon {
                    order: 0;
                    margin: 0 auto;
                }

                .sticky-bar-actions {
                    order: 2;
                    width: 100%;
                    justify-content: center;
                }

                .btn-sticky-primary,
                .btn-sticky-secondary {
                    flex: 1;
                    justify-content: center;
                    max-width: 150px;
                }

                .btn-close-sticky {
                    position: absolute;
                    top: 0.5rem;
                    right: 0.5rem;
                    margin: 0;
                }

                .sticky-title {
                    font-size: 1rem;
                }

                .sticky-subtitle {
                    font-size: 0.85rem;
                }
            }

            @media (max-width: 480px) {
                .sticky-bar-actions {
                    flex-direction: column;
                    width: 100%;
                }

                .btn-sticky-primary,
                .btn-sticky-secondary {
                    width: 100%;
                    max-width: none;
                }
            }
        `;

        document.head.appendChild(styles);
    }

    setupTimeBasedTriggers() {
        // Show offer after 30 seconds
        setTimeout(() => {
            if (!this.timeBasedShown && this.userBehavior.engagementScore > 30) {
                this.showTimeBasedOffer();
            }
        }, 30000);

        // Show different offer after 2 minutes
        setTimeout(() => {
            if (this.userBehavior.engagementScore > 50) {
                this.showExtendedTimeOffer();
            }
        }, 120000);
    }

    showTimeBasedOffer() {
        this.timeBasedShown = true;

        // Track time-based engagement but don't show notification
        this.trackConversionEvent('time_based_offer_shown');
        console.log('Time-based engagement detected');
    }

    showExtendedTimeOffer() {
        const modal = this.createModal({
            title: 'Oferta Especial para Você!',
            content: `
                <div class="special-offer-content">
                    <h3>Você demonstrou interesse real em nossos serviços!</h3>
                    <div class="offer-highlight">
                        <div class="offer-badge">OFERTA LIMITADA</div>
                        <div class="offer-details">
                            <h4>Primeira Mensalidade GRÁTIS</h4>
                            <p>Para novos clientes que fecharem hoje</p>
                        </div>
                    </div>
                    <div class="offer-benefits">
                        <div class="benefit">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                            Sem taxa de setup
                        </div>
                        <div class="benefit">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                            Migração gratuita
                        </div>
                        <div class="benefit">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                            Suporte prioritário
                        </div>
                    </div>
                    <div class="urgency-timer" id="offerTimer">
                        <span>Oferta válida por: <strong>10:00</strong></span>
                    </div>
                    <div class="offer-actions">
                        <a href="${this.getWhatsAppURL('Olá! Quero aproveitar a oferta da primeira mensalidade grátis!')}"
                           target="_blank" class="btn-primary">Quero Aproveitar</a>
                        <button class="btn-secondary" onclick="this.closest('.conversion-modal').remove()">Não, obrigado</button>
                    </div>
                </div>
            `,
            type: 'special-offer'
        });

        this.startOfferTimer();
        this.trackConversionEvent('extended_time_offer_shown');
    }

    setupUrgencyIndicators() {
        // Add urgency indicators to key elements
        const urgencyMessages = [
            'Transforme sua gestão contábil hoje mesmo',
            'Comece sua jornada de sucesso empresarial',
            'Junte-se a mais de 500 empresas satisfeitas',
            'Descubra como economizar na sua contabilidade'
        ];

        // Add to calculator page
        if (window.location.pathname.includes('calculadora')) {
            this.addUrgencyBanner(urgencyMessages[0]);
        }

        // Add to forms
        setTimeout(() => {
            const forms = document.querySelectorAll('form');
            forms.forEach((form, index) => {
                if (index < urgencyMessages.length) {
                    this.addUrgencyToForm(form, urgencyMessages[index]);
                }
            });
        }, 2000);
    }

    addUrgencyBanner(message) {
        const banner = document.createElement('div');
        banner.className = 'urgency-banner';
        banner.innerHTML = `
            <div class="urgency-content">
                <span class="urgency-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 2v11h3v9l7-12h-4l4-8z"/>
                    </svg>
                </span>
                <span class="urgency-text">${message}</span>
                <span class="urgency-pulse"></span>
            </div>
        `;

        document.body.insertBefore(banner, document.body.firstChild);
    }

    addUrgencyToForm(form, message) {
        const urgencyDiv = document.createElement('div');
        urgencyDiv.className = 'form-urgency';
        urgencyDiv.innerHTML = `<span class="urgency-indicator">${message}</span>`;

        form.insertBefore(urgencyDiv, form.firstChild);
    }

    setupSocialProof() {
        // Show recent activity notifications
        const activities = [
            'João S. descobriu como economizar na contabilidade',
            'Maria L. transformou a gestão da sua empresa',
            'Carlos M. encontrou a solução ideal para seu negócio',
            'Ana P. iniciou sua jornada empresarial conosco',
            'Roberto F. está calculando sua economia mensal'
        ];

        let activityIndex = 0;

        setInterval(() => {
            if (Math.random() > 0.7) { // 30% chance every interval
                this.showSocialProofNotification(activities[activityIndex]);
                activityIndex = (activityIndex + 1) % activities.length;
            }
        }, 45000); // Every 45 seconds
    }

    showSocialProofNotification(activity) {
        const notification = document.createElement('div');
        notification.className = 'social-proof-notification';
        notification.innerHTML = `
            <div class="social-proof-content">
                <div class="social-proof-avatar">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                    </svg>
                </div>
                <div class="social-proof-text">${activity}</div>
                <div class="social-proof-time">agora</div>
            </div>
        `;

        document.body.appendChild(notification);

        // Show animation
        setTimeout(() => notification.classList.add('show'), 100);

        // Hide after 5 seconds
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 5000);

        this.trackConversionEvent('social_proof_shown', { activity });
    }

    setupPersonalization() {
        // Personalize based on user behavior
        setTimeout(() => {
            const engagement = this.userBehavior.engagementScore;

            if (engagement > 70) {
                this.personalizeForHighEngagement();
            } else if (engagement > 40) {
                this.personalizeForMediumEngagement();
            } else if (engagement > 20) {
                this.personalizeForLowEngagement();
            }
        }, 20000);
    }

    personalizeForHighEngagement() {
        // User is highly engaged - track but don't show intrusive elements
        this.trackConversionEvent('high_engagement_personalization');

        // Optional: Could add subtle personalization here if needed
        console.log('High engagement user detected');
    }

    personalizeForMediumEngagement() {
        // Track medium engagement but don't show notifications
        this.trackConversionEvent('medium_engagement_personalization');
        console.log('Medium engagement user detected');
    }

    personalizeForLowEngagement() {
        // Track low engagement but don't show notifications
        this.trackConversionEvent('low_engagement_personalization');
        console.log('Low engagement user detected');
    }

    setupRetargeting() {
        // Set retargeting pixels and cookies
        this.setRetargetingData();

        // Track page visits for retargeting
        this.trackConversionEvent('page_visit_for_retargeting', {
            page: window.location.pathname,
            timestamp: new Date().toISOString(),
            user_engagement: this.userBehavior.engagementScore
        });
    }

    setRetargetingData() {
        // Set cookies for retargeting campaigns
        const retargetingData = {
            visited_pages: JSON.parse(localStorage.getItem('logyc_visited_pages') || '[]'),
            last_visit: new Date().toISOString(),
            engagement_level: this.userBehavior.engagementScore,
            interests: this.detectUserInterests()
        };

        retargetingData.visited_pages.push(window.location.pathname);
        retargetingData.visited_pages = [...new Set(retargetingData.visited_pages)]; // Remove duplicates

        localStorage.setItem('logyc_retargeting_data', JSON.stringify(retargetingData));
        localStorage.setItem('logyc_visited_pages', JSON.stringify(retargetingData.visited_pages));
    }

    detectUserInterests() {
        const interests = [];
        const path = window.location.pathname;

        if (path.includes('calculadora')) interests.push('pricing');
        if (path.includes('troca')) interests.push('switching');
        if (path.includes('mei')) interests.push('mei');
        if (path.includes('blog')) interests.push('education');

        return interests;
    }

    // Utility methods
    createModal(options) {
        const modal = document.createElement('div');
        modal.className = `conversion-modal ${options.type || ''}`;
        modal.innerHTML = `
            <div class="modal-overlay" onclick="this.parentElement.remove()"></div>
            <div class="modal-content">
                <div class="modal-header">
                    <h2>${options.title}</h2>
                    <button class="modal-close" onclick="this.closest('.conversion-modal').remove()">×</button>
                </div>
                <div class="modal-body">
                    ${options.content}
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        setTimeout(() => modal.classList.add('show'), 100);

        return modal;
    }

    createNotification(options) {
        if (window.notifications) {
            return window.notifications.show({
                type: 'info',
                title: options.title,
                message: options.message,
                actions: options.actions,
                duration: options.duration || 8000
            });
        }
    }

    getWhatsAppURL(message = null) {
        const defaultMessage = 'Olá! Gostaria de saber mais sobre os serviços da Logyc Contabilidade.';
        const finalMessage = message || defaultMessage;
        return `https://wa.me/5541987427111?text=${encodeURIComponent(finalMessage)}`;
    }

    startOfferTimer() {
        let timeLeft = 600; // 10 minutes
        const timer = document.getElementById('offerTimer');

        const countdown = setInterval(() => {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;

            if (timer) {
                timer.innerHTML = `Oferta válida por: <strong>${minutes}:${seconds.toString().padStart(2, '0')}</strong>`;
            }

            timeLeft--;

            if (timeLeft < 0) {
                clearInterval(countdown);
                if (timer) {
                    timer.innerHTML = '<span style="color: red;">Oferta expirada</span>';
                }
            }
        }, 1000);
    }

    trackConversionEvent(eventName, data = {}) {
        if (window.advancedAnalytics) {
            window.advancedAnalytics.trackEvent(`conversion_${eventName}`, {
                ...data,
                engagement_score: this.userBehavior.engagementScore,
                time_on_page: this.userBehavior.timeSpent
            });
        }
    }

    // Public methods
    forceShowExitIntent() {
        this.showExitIntentModal();
    }

    forceShowSpecialOffer() {
        this.showExtendedTimeOffer();
    }

    getConversionData() {
        return {
            userBehavior: this.userBehavior,
            triggersShown: {
                exitIntent: this.exitIntentShown,
                scrollBased: this.scrollBasedShown,
                timeBased: this.timeBasedShown
            }
        };
    }
}

// Add conversion optimization styles
const conversionStyles = `
    .conversion-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 10000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .conversion-modal.show {
        opacity: 1;
        visibility: visible;
    }

    .modal-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
    }

    .modal-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: white;
        border-radius: 16px;
        max-width: 550px;
        width: 90%;
        max-height: 85vh;
        overflow-y: auto;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .modal-header {
        padding: 2rem 1.5rem 1.5rem;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
        border-radius: 16px 16px 0 0;
    }

    .modal-header h2 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 700;
        color: #005aec;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    .modal-close {
        background: rgba(0, 0, 0, 0.05);
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #666;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .modal-close:hover {
        background: rgba(0, 0, 0, 0.1);
        color: #333;
        transform: scale(1.1);
    }

    .modal-body {
        padding: 2rem 1.5rem;
        background: #ffffff;
        border-radius: 0 0 16px 16px;
    }

    .exit-intent-content h3 {
        color: var(--azul-escuro, #005aec);
        margin-bottom: 1rem;
    }

    .exit-intent-benefits {
        margin: 1.5rem 0;
    }

    .benefit {
        padding: 0.5rem 0;
        color: var(--verde-escuro, #217345);
        font-weight: 500;
    }

    .exit-intent-actions {
        display: flex;
        gap: 1rem;
        margin-top: 1.5rem;
    }

    .floating-conversion-offer {
        position: fixed;
        bottom: 20px;
        left: 20px;
        background: linear-gradient(45deg, #01d800, #217345);
        color: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 4px 20px rgba(1, 216, 0, 0.3);
        z-index: 1000;
        max-width: 400px;
        animation: slideInLeft 0.5s ease;
    }

    .floating-offer-content {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .offer-text {
        flex: 1;
    }

    .offer-text strong {
        display: block;
        margin-bottom: 0.25rem;
    }

    .offer-actions {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .btn-offer {
        background: white;
        color: #01d800;
        padding: 0.5rem 1rem;
        border-radius: 6px;
        text-decoration: none;
        font-weight: bold;
        text-align: center;
        transition: all 0.3s;
    }

    .btn-offer:hover {
        background: #f0f0f0;
    }

    .btn-close {
        background: none;
        border: none;
        color: white;
        font-size: 1.2rem;
        cursor: pointer;
        opacity: 0.7;
    }

    .sticky-conversion-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        background: var(--azul-escuro, #005aec);
        color: white;
        z-index: 1000;
        animation: slideInUp 0.5s ease;
    }

    .sticky-bar-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 1rem;
        max-width: 1200px;
        margin: 0 auto;
    }

    .btn-sticky {
        background: var(--verde-claro, #01d800);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        text-decoration: none;
        font-weight: bold;
        transition: all 0.3s;
    }

    .btn-sticky:hover {
        background: var(--verde-escuro, #217345);
    }

    .urgency-banner {
        background: linear-gradient(45deg, #fd0e35, #ff4757);
        color: white;
        padding: 0.75rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .urgency-content {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        font-weight: bold;
    }

    .urgency-pulse {
        width: 8px;
        height: 8px;
        background: white;
        border-radius: 50%;
        animation: pulse 1s infinite;
    }

    .social-proof-notification {
        position: fixed;
        bottom: 100px;
        left: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        padding: 1rem;
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        max-width: 300px;
    }

    .social-proof-notification.show {
        transform: translateX(0);
    }

    .social-proof-content {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .social-proof-avatar {
        width: 40px;
        height: 40px;
        background: var(--azul-escuro, #005aec);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    .social-proof-text {
        flex: 1;
        font-size: 0.9rem;
        color: #333;
    }

    .social-proof-time {
        font-size: 0.8rem;
        color: #666;
    }

    @keyframes slideInLeft {
        from { transform: translateX(-100%); }
        to { transform: translateX(0); }
    }

    @keyframes slideInUp {
        from { transform: translateY(100%); }
        to { transform: translateY(0); }
    }

    /* Estilos para Oferta Especial */
    .special-offer-content {
        text-align: center;
        padding: 1rem 0;
    }

    .special-offer-content h3 {
        color: var(--azul-escuro, #005aec);
        margin-bottom: 1.5rem;
        font-size: 1.3rem;
        font-weight: 600;
    }

    .offer-highlight {
        background: linear-gradient(135deg, #01d800, #217345);
        border-radius: 12px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .offer-highlight::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        animation: shimmer 2s infinite;
    }

    .offer-badge {
        background: #fd0e35;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-bottom: 1rem;
        display: inline-block;
        animation: pulse 2s infinite;
    }

    .offer-details h4 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
        font-weight: bold;
    }

    .offer-details p {
        font-size: 1rem;
        opacity: 0.9;
        margin: 0;
    }

    .offer-benefits {
        margin: 1.5rem 0;
        text-align: left;
    }

    .urgency-timer {
        background: rgba(253, 14, 53, 0.1);
        border: 2px solid #fd0e35;
        border-radius: 8px;
        padding: 1rem;
        margin: 1.5rem 0;
        color: #fd0e35;
        font-weight: bold;
        animation: pulse-border 1.5s infinite;
    }

    .urgency-timer strong {
        font-size: 1.2rem;
        color: #fd0e35;
    }

    .special-offer-content .offer-actions {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        align-items: center;
        justify-content: center;
        margin-top: 2rem;
        width: 100%;
    }

    .special-offer-content .btn-primary {
        background: linear-gradient(135deg, #01d800, #217345);
        color: white;
        padding: 1rem 2rem;
        border: none;
        border-radius: 8px;
        font-weight: bold;
        text-decoration: none;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(1, 216, 0, 0.3);
        position: relative;
        overflow: hidden;
        width: 280px;
        max-width: 90%;
        text-align: center;
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .special-offer-content .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(1, 216, 0, 0.4);
    }

    .special-offer-content .btn-secondary {
        background: transparent;
        color: #666;
        padding: 1rem 2rem;
        border: 2px solid #ddd;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 280px;
        max-width: 90%;
        text-align: center;
    }

    .special-offer-content .btn-secondary:hover {
        background: #f5f5f5;
        border-color: #999;
        color: #333;
        transform: translateY(-1px);
    }

    @keyframes shimmer {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    @keyframes pulse-border {
        0%, 100% { border-color: #fd0e35; }
        50% { border-color: #ff4757; }
    }

    @media (max-width: 768px) {
        .floating-conversion-offer {
            left: 10px;
            right: 10px;
            max-width: none;
        }

        .floating-offer-content {
            flex-direction: column;
            text-align: center;
        }

        .sticky-bar-content {
            flex-direction: column;
            gap: 0.5rem;
            text-align: center;
        }

        .special-offer-content .offer-actions {
            flex-direction: column;
            gap: 0.75rem;
            align-items: center;
            width: 100%;
        }

        .special-offer-content .btn-primary,
        .special-offer-content .btn-secondary {
            width: 100%;
            max-width: 320px;
            padding: 0.875rem 1.5rem;
            text-align: center;
        }

        .offer-highlight {
            padding: 1rem;
        }

        .offer-details h4 {
            font-size: 1.3rem;
        }
    }
`;

// Add styles
const conversionStyleSheet = document.createElement('style');
conversionStyleSheet.textContent = conversionStyles;
document.head.appendChild(conversionStyleSheet);

// Initialize conversion optimizer
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.conversionOptimizer = new ConversionOptimizer();

        // Função de teste para mostrar a oferta especial (remover em produção)
        window.testSpecialOffer = function() {
            if (window.conversionOptimizer) {
                window.conversionOptimizer.forceShowSpecialOffer();
            }
        };

        // Adicionar botão de teste temporário (remover em produção)
        if (window.location.hostname === 'localhost' || window.location.protocol === 'file:') {
            const testButton = document.createElement('button');
            testButton.textContent = 'Testar Oferta Especial';
            testButton.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                z-index: 9999;
                background: #ff4757;
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 12px;
            `;
            testButton.onclick = window.testSpecialOffer;
            document.body.appendChild(testButton);
        }
    }, 1000);
});

// Export for global use
window.ConversionOptimizer = ConversionOptimizer;
