// JavaScript específico para o formulário "Troca de Contabilidade"
document.addEventListener('DOMContentLoaded', function() {
    const switchForm = document.getElementById('switchForm');

    if (switchForm) {
        // Controle de campos condicionais
        setupConditionalFields();

        // Máscara para CNPJ
        setupCNPJMask();

        // Máscara para faturamento
        setupFaturamentoMask();

        // Submissão do formulário
        switchForm.addEventListener('submit', function(e) {
            e.preventDefault();

            if (validateSwitchForm()) {
                handleSwitchFormSubmission();
            }
        });
    }
});

function setupConditionalFields() {
    // Campo CNPJ
    const possuiCNPJRadios = document.querySelectorAll('input[name="possui_cnpj"]');
    const cnpjGroup = document.getElementById('cnpj-group');
    const cnpjInput = document.getElementById('cnpj');

    possuiCNPJRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'sim') {
                cnpjGroup.style.display = 'flex';
                cnpjInput.setAttribute('required', 'required');
                cnpjInput.focus();
            } else {
                cnpjGroup.style.display = 'none';
                cnpjInput.removeAttribute('required');
                cnpjInput.value = '';
            }
        });
    });

    // Campo Funcionários
    const possuiFuncionariosRadios = document.querySelectorAll('input[name="possui_funcionarios"]');
    const funcionariosGroup = document.getElementById('funcionarios-group');
    const funcionariosInput = document.getElementById('qtd_funcionarios');

    possuiFuncionariosRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'sim') {
                funcionariosGroup.style.display = 'flex';
                funcionariosInput.setAttribute('required', 'required');
                funcionariosInput.focus();
            } else {
                funcionariosGroup.style.display = 'none';
                funcionariosInput.removeAttribute('required');
                funcionariosInput.value = '';
            }
        });
    });
}

function setupCNPJMask() {
    const cnpjInput = document.getElementById('cnpj');

    if (cnpjInput) {
        cnpjInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');

            // Limita a 14 dígitos
            if (value.length > 14) {
                value = value.substring(0, 14);
            }

            // Aplica a máscara
            if (value.length > 0) {
                value = value.replace(/^(\d{2})(\d)/, '$1.$2');
                value = value.replace(/^(\d{2})\.(\d{3})(\d)/, '$1.$2.$3');
                value = value.replace(/\.(\d{3})(\d)/, '.$1/$2');
                value = value.replace(/(\d{4})(\d)/, '$1-$2');
            }

            e.target.value = value;
        });

        // Validação em tempo real
        cnpjInput.addEventListener('blur', function() {
            const cnpj = this.value.replace(/\D/g, '');
            if (cnpj.length > 0 && cnpj.length !== 14) {
                this.style.borderColor = '#fd0e35';
                showFieldError(this, 'CNPJ deve ter 14 dígitos');
            } else if (cnpj.length === 14 && !isValidCNPJ(cnpj)) {
                this.style.borderColor = '#fd0e35';
                showFieldError(this, 'CNPJ inválido');
            } else {
                this.style.borderColor = '#ddd';
                hideFieldError(this);
            }
        });
    }
}

function setupFaturamentoMask() {
    const faturamentoInput = document.getElementById('faturamento');

    if (faturamentoInput) {
        faturamentoInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');

            // Converte para formato monetário
            if (value.length > 0) {
                value = (parseInt(value) / 100).toFixed(2);
                value = value.replace('.', ',');
                value = value.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
                value = 'R$ ' + value;
            }

            e.target.value = value;
        });
    }
}

function validateSwitchForm() {
    const form = document.getElementById('switchForm');
    const formData = new FormData(form);
    let isValid = true;
    let errors = [];

    // Limpar estilos de erro anteriores
    clearErrorStyles();

    // Validar campos obrigatórios básicos
    const requiredFields = ['nome', 'whatsapp', 'email', 'cidade', 'faturamento', 'possui_cnpj', 'possui_funcionarios'];

    requiredFields.forEach(field => {
        const value = formData.get(field);
        if (!value || value.trim() === '') {
            isValid = false;
            errors.push(`O campo ${getFieldLabel(field)} é obrigatório.`);
            highlightErrorField(field);
        }
    });

    // Validação condicional para CNPJ
    const possuiCNPJ = formData.get('possui_cnpj');
    const cnpj = formData.get('cnpj');

    if (possuiCNPJ === 'sim') {
        if (!cnpj || cnpj.trim() === '') {
            isValid = false;
            errors.push('Quando "Possui CNPJ" é marcado como SIM, o CNPJ deve ser informado.');
            highlightErrorField('cnpj');
        } else {
            const cnpjNumbers = cnpj.replace(/\D/g, '');
            if (cnpjNumbers.length !== 14) {
                isValid = false;
                errors.push('CNPJ deve ter 14 dígitos.');
                highlightErrorField('cnpj');
            } else if (!isValidCNPJ(cnpjNumbers)) {
                isValid = false;
                errors.push('CNPJ informado é inválido.');
                highlightErrorField('cnpj');
            }
        }
    }

    // Validação condicional para funcionários
    const possuiFuncionarios = formData.get('possui_funcionarios');
    const qtdFuncionarios = formData.get('qtd_funcionarios');

    if (possuiFuncionarios === 'sim') {
        if (!qtdFuncionarios || qtdFuncionarios.trim() === '' || parseInt(qtdFuncionarios) < 1) {
            isValid = false;
            errors.push('Quando "Possui Funcionários" é marcado como SIM, a quantidade deve ser informada (mínimo 1).');
            highlightErrorField('qtd_funcionarios');
        }
    }

    // Validação de email
    const email = formData.get('email');
    if (email && !isValidEmail(email)) {
        isValid = false;
        errors.push('Por favor, insira um email válido.');
        highlightErrorField('email');
    }

    // Validação de WhatsApp
    const whatsapp = formData.get('whatsapp');
    if (whatsapp && !isValidWhatsApp(whatsapp)) {
        isValid = false;
        errors.push('Por favor, insira um número de WhatsApp válido.');
        highlightErrorField('whatsapp');
    }

    // Validação de faturamento
    const faturamento = formData.get('faturamento');
    if (faturamento) {
        const faturamentoValue = parseFloat(faturamento.replace(/[^\d,]/g, '').replace(',', '.'));
        if (isNaN(faturamentoValue) || faturamentoValue <= 0) {
            isValid = false;
            errors.push('Por favor, insira um valor de faturamento válido.');
            highlightErrorField('faturamento');
        }
    }

    if (!isValid) {
        showErrors(errors);
        // Scroll para o primeiro erro
        const firstErrorField = document.querySelector('.error-field');
        if (firstErrorField) {
            firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    return isValid;
}

function handleSwitchFormSubmission() {
    const form = document.getElementById('switchForm');
    const formData = new FormData(form);

    // Coleta os dados do formulário
    const dados = {
        nome: formData.get('nome'),
        possui_cnpj: formData.get('possui_cnpj'),
        cnpj: formData.get('cnpj') || 'Não possui',
        whatsapp: formData.get('whatsapp'),
        email: formData.get('email'),
        cidade: formData.get('cidade'),
        faturamento: formData.get('faturamento'),
        possui_funcionarios: formData.get('possui_funcionarios'),
        qtd_funcionarios: formData.get('qtd_funcionarios') || '0'
    };

    // Cria mensagem para WhatsApp
    const mensagemWhatsApp = criarMensagemWhatsAppSwitch(dados);

    // Mostra sucesso
    showSuccessMessage('Sua solicitação de troca de contabilidade foi enviada com sucesso! Nossa equipe entrará em contato em breve para apresentar nossa proposta personalizada.');

    // Limpa o formulário
    form.reset();

    // Esconde campos condicionais
    document.getElementById('cnpj-group').style.display = 'none';
    document.getElementById('funcionarios-group').style.display = 'none';

    console.log('Dados do formulário "Troca de Contabilidade":', dados);
    console.log('Mensagem WhatsApp:', mensagemWhatsApp);

    // Abrir WhatsApp
    window.open(`https://wa.me/5541987427111?text=${encodeURIComponent(mensagemWhatsApp)}`, '_blank');
}

function criarMensagemWhatsAppSwitch(dados) {
    return `*Solicitação de Troca de Contabilidade - Logyc*

*Nome:* ${dados.nome}
*WhatsApp:* ${dados.whatsapp}
*Email:* ${dados.email}
*Cidade:* ${dados.cidade}

*Informações da Empresa:*
*Possui CNPJ:* ${dados.possui_cnpj.toUpperCase()}
*CNPJ:* ${dados.cnpj}
*Faturamento Médio Mensal:* ${dados.faturamento}
*Possui Funcionários:* ${dados.possui_funcionarios.toUpperCase()}
*Quantidade de Funcionários:* ${dados.qtd_funcionarios}

_Enviado através da página "Trocar de Contabilidade" do site da Logyc Contabilidade_`;
}

function isValidCNPJ(cnpj) {
    // Algoritmo de validação de CNPJ
    if (cnpj.length !== 14) return false;

    // Elimina CNPJs inválidos conhecidos
    if (/^(\d)\1+$/.test(cnpj)) return false;

    // Valida DVs
    let tamanho = cnpj.length - 2;
    let numeros = cnpj.substring(0, tamanho);
    let digitos = cnpj.substring(tamanho);
    let soma = 0;
    let pos = tamanho - 7;

    for (let i = tamanho; i >= 1; i--) {
        soma += numeros.charAt(tamanho - i) * pos--;
        if (pos < 2) pos = 9;
    }

    let resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
    if (resultado != digitos.charAt(0)) return false;

    tamanho = tamanho + 1;
    numeros = cnpj.substring(0, tamanho);
    soma = 0;
    pos = tamanho - 7;

    for (let i = tamanho; i >= 1; i--) {
        soma += numeros.charAt(tamanho - i) * pos--;
        if (pos < 2) pos = 9;
    }

    resultado = soma % 11 < 2 ? 0 : 11 - soma % 11;
    if (resultado != digitos.charAt(1)) return false;

    return true;
}

function getFieldLabel(fieldName) {
    const labels = {
        'nome': 'Nome',
        'whatsapp': 'Número WhatsApp',
        'email': 'Email',
        'cidade': 'Cidade',
        'faturamento': 'Faturamento Médio Mensal',
        'possui_cnpj': 'Possui CNPJ',
        'possui_funcionarios': 'Possui Funcionários Ativos',
        'cnpj': 'CNPJ',
        'qtd_funcionarios': 'Quantidade de Funcionários'
    };
    return labels[fieldName] || fieldName;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function isValidWhatsApp(whatsapp) {
    const cleanNumber = whatsapp.replace(/\D/g, '');
    return cleanNumber.length >= 10 && cleanNumber.length <= 15;
}

function clearErrorStyles() {
    const errorFields = document.querySelectorAll('.error-field');
    errorFields.forEach(field => {
        field.classList.remove('error-field');
        field.style.borderColor = '#ddd';
        field.style.boxShadow = 'none';
    });

    // Remove mensagens de erro de campo
    const fieldErrors = document.querySelectorAll('.field-error');
    fieldErrors.forEach(error => error.remove());
}

function highlightErrorField(fieldName) {
    const field = document.getElementById(fieldName) || document.querySelector(`input[name="${fieldName}"]`);
    if (field) {
        field.classList.add('error-field');
        field.style.borderColor = '#fd0e35';
        field.style.boxShadow = '0 0 5px rgba(253, 14, 53, 0.3)';

        // Para radio buttons, destacar o grupo todo
        if (field.type === 'radio') {
            const radioGroup = field.closest('.radio-group');
            if (radioGroup) {
                radioGroup.style.borderLeft = '3px solid #fd0e35';
                radioGroup.style.paddingLeft = '1rem';
            }
        }
    }
}

function showFieldError(field, message) {
    // Remove erro anterior se existir
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }

    // Cria nova mensagem de erro
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.style.cssText = `
        color: #fd0e35;
        font-size: 0.8rem;
        margin-top: 0.25rem;
    `;
    errorDiv.textContent = message;

    field.parentNode.appendChild(errorDiv);
}

function hideFieldError(field) {
    const errorDiv = field.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.remove();
    }
}

function showErrors(errors) {
    const existingAlert = document.querySelector('.error-alert');
    if (existingAlert) {
        existingAlert.remove();
    }

    const alertDiv = document.createElement('div');
    alertDiv.className = 'error-alert';
    alertDiv.style.cssText = `
        background: #fd0e35;
        color: white;
        padding: 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
        position: fixed;
        top: 100px;
        right: 20px;
        max-width: 400px;
        z-index: 1001;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        animation: slideIn 0.3s ease;
    `;

    alertDiv.innerHTML = `
        <strong>Erro no formulário:</strong><br>
        ${errors.join('<br>')}
        <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; color: white; font-size: 1.2rem; cursor: pointer; margin-top: -0.5rem;">&times;</button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => alertDiv.remove(), 300);
        }
    }, 7000);
}

function showSuccessMessage(message) {
    const successDiv = document.createElement('div');
    successDiv.className = 'success-alert';
    successDiv.style.cssText = `
        background: #01d800;
        color: white;
        padding: 1rem;
        border-radius: 5px;
        margin-bottom: 1rem;
        position: fixed;
        top: 100px;
        right: 20px;
        max-width: 400px;
        z-index: 1001;
        box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        animation: slideIn 0.3s ease;
    `;

    successDiv.innerHTML = `
        <strong>Sucesso!</strong><br>
        ${message}
        <button onclick="this.parentElement.remove()" style="float: right; background: none; border: none; color: white; font-size: 1.2rem; cursor: pointer; margin-top: -0.5rem;">&times;</button>
    `;

    document.body.appendChild(successDiv);

    setTimeout(() => {
        if (successDiv.parentElement) {
            successDiv.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => successDiv.remove(), 300);
        }
    }, 6000);
}
