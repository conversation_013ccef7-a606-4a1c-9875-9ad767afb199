// Sistema de Blog - Logyc Contabilidade
class BlogSystem {
    constructor() {
        this.setupStyles(); // Move setupStyles para o construtor para garantir que os estilos sejam carregados primeiro
        this.articles = [];
        this.currentCategory = 'all';
        this.searchTerm = '';
        this.currentPage = 1;
        this.articlesPerPage = 6;
        this.init();
    }

    async init() {
        try {
            console.log('Iniciando carregamento do blog...');
            this.articles = await this.getBlogData();
            console.log('Artigos carregados:', this.articles);
            this.renderArticles();
            this.setupEventListeners();
        } catch (error) {
            console.error('Erro ao inicializar o blog:', error);
            document.getElementById('blogGrid').innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 3rem;">
                    <h3>Erro ao carregar os artigos</h3>
                    <p>Por favor, tente novamente mais tarde.</p>
                    <p style="color: red; font-size: 0.8rem;">Erro: ${error.message}</p>
                </div>
            `;
        }
    }


    async getBlogData() {
        const articles = [];
        const postFiles = [
            { id: 1, file: 'post-1-como-abrir-empresa-2024.md' },
            { id: 2, file: 'post-2-mei-2024-guia-completo.md' },
            { id: 3, file: 'post-3-simples-nacional-vantagens.md' },
            { id: 4, file: 'post-4-departamento-pessoal-obrigacoes.md' },
            { id: 5, file: 'post-5-dicas-organizar-contabilidade.md' },
            { id: 6, file: 'post-6-declaracao-imposto-renda-empresas.md' },
            { id: 7, file: 'post-7-trocar-contabilidade-sem-complicacoes.md' },
            { id: 8, file: 'post-8-esocial-guia-completo.md' }
        ];

        // Mapeia os arquivos para categorias
        const categoryMap = {
            'post-1': 'abertura',
            'post-2': 'mei',
            'post-3': 'fiscal',
            'post-4': 'pessoal',
            'post-5': 'dicas',
            'post-6': 'fiscal',
            'post-7': 'dicas',
            'post-8': 'pessoal'
        };

        let hasError = false;
        for (const post of postFiles) {
            try {
                console.log(`Tentando carregar arquivo ${post.id}/${postFiles.length}:`, post.file);
                const content = await this.readMarkdownFile(post.file);
                if (!content) {
                    console.error(`Arquivo vazio ou não encontrado: ${post.file}`);
                    hasError = true;
                    continue;
                }

                // Extrai os metadados do conteúdo markdown
                const lines = content.split('\n');
                console.log(`Conteúdo do arquivo ${post.file}:`, {
                    primeiraLinha: lines[0],
                    terceiraLinha: lines[2],
                    quintaLinha: lines[4]
                });

                const title = lines[0].replace('# ', '');
                const metaLine = lines[2];
                const [author, date, readTime] = metaLine
                    .replace(/\*\*/g, '')
                    .split('|')
                    .map(s => s.trim());

                // Extrai o resumo (primeiro parágrafo após o título)
                const excerpt = lines[4].trim();

                // Determina a categoria baseado no nome do arquivo
                const fileNameMatch = post.file.match(/post-\d/);
                if (!fileNameMatch) {
                    throw new Error(`Nome de arquivo inválido: ${post.file}`);
                }
                const category = categoryMap[fileNameMatch[0]];

                // Define as tags baseado no conteúdo
                const tags = this.extractTags(content);

                // Cria o objeto do artigo
                articles.push({
                    id: post.id,
                    title,
                    excerpt,
                    content,
                    category,
                    author: author.replace('Por: ', ''),
                    date: this.formatDateFromText(date.replace('Data: ', '')),
                    readTime: readTime.replace('Tempo de leitura: ', ''),
                    image: this.getImageForCategory(category),
                    tags,
                    featured: post.id === 1
                });
                console.log(`Artigo ${post.id} processado com sucesso:`, { title, category });
            } catch (error) {
                console.error(`Erro ao processar ${post.file}:`, error);
                hasError = true;
            }
        }

        if (articles.length === 0) {
            throw new Error(hasError ?
                'Nenhum artigo pôde ser carregado devido a erros. Verifique o console para mais detalhes.' :
                'Nenhum artigo encontrado na pasta conteudo/'
            );
        }

        return articles;
    }

    async readMarkdownFile(filePath) {
        console.log('Tentando carregar arquivo:', filePath);
        try {
            // Carrega o arquivo markdown real da pasta conteudo/
            const fullPath = `conteudo/${filePath}`;
            const response = await fetch(fullPath);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const content = await response.text();

            if (!content || content.trim().length === 0) {
                throw new Error('Arquivo vazio ou conteúdo inválido');
            }

            console.log('Arquivo carregado com sucesso:', filePath);
            return content;
        } catch (error) {
            console.error(`Erro ao carregar arquivo ${filePath}:`, error);
            throw new Error(`Falha ao carregar ${filePath}: ${error.message}`);
        }
    }

    formatDateFromText(dateText) {
        const [day, month, year] = dateText.split('/');
        return `${year}-${month}-${day}`;
    }

    extractTags(content) {
        // Extrai palavras-chave do conteúdo
        const commonTags = {
            'abertura': ['abertura', 'cnpj', 'empresa', 'documentos'],
            'mei': ['mei', 'microempreendedor', 'faturamento', 'obrigações'],
            'fiscal': ['fiscal', 'impostos', 'tributário', 'simples nacional'],
            'pessoal': ['trabalhista', 'funcionários', 'obrigações', 'esocial'],
            'dicas': ['organização', 'contabilidade', 'gestão', 'documentos']
        };

        return content.toLowerCase().split('\n')
            .filter(line => line.startsWith('## ') || line.startsWith('### '))
            .map(line => line.replace(/^#+ /, '').toLowerCase())
            .slice(0, 4);
    }

    getImageForCategory(category) {
        const images = {
            'abertura': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=250&fit=crop&auto=format',
            'mei': 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=250&fit=crop&auto=format',
            'fiscal': 'https://images.unsplash.com/photo-1554224155-8d04cb21cd6c?w=400&h=250&fit=crop&auto=format',
            'pessoal': 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=400&h=250&fit=crop&auto=format',
            'dicas': 'https://images.unsplash.com/photo-1554224154-22dec7ec8818?w=400&h=250&fit=crop&auto=format'
        };
        return images[category] || images['dicas'];
    }

    setupStyles() {
        const styles = `
            .blog-section {
                padding: 120px 0 80px;
                background: var(--cinza-claro, #f8f9fa);
                min-height: 100vh;
            }

            .blog-header {
                text-align: center;
                margin-bottom: 3rem;
            }

            .blog-header h1 {
                color: var(--azul-escuro, #005aec);
                font-size: 2.5rem;
                margin-bottom: 1rem;
            }

            .blog-header p {
                color: var(--cinza-escuro, #666);
                font-size: 1.1rem;
            }

            .blog-filters {
                display: flex;
                justify-content: center;
                flex-wrap: wrap;
                gap: 0.5rem;
                margin-bottom: 2rem;
            }

            .filter-btn {
                background: white;
                border: 1px solid #ddd;
                border-radius: 20px;
                padding: 8px 16px;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.3s;
                white-space: nowrap;
            }

            .filter-btn:hover,
            .filter-btn.active {
                background: var(--azul-escuro, #005aec);
                color: white;
                border-color: var(--azul-escuro, #005aec);
            }

            .blog-search {
                max-width: 400px;
                margin: 0 auto 3rem;
                display: flex;
                position: relative;
            }

            .blog-search input {
                flex: 1;
                padding: 12px 16px;
                border: 1px solid #ddd;
                border-radius: 25px;
                font-size: 16px;
                outline: none;
            }

            .blog-search button {
                position: absolute;
                right: 5px;
                top: 50%;
                transform: translateY(-50%);
                background: var(--azul-escuro, #005aec);
                color: white;
                border: none;
                border-radius: 50%;
                width: 35px;
                height: 35px;
                cursor: pointer;
            }

            .blog-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                gap: 2rem;
                margin-bottom: 3rem;
            }

            .blog-card {
                background: white;
                border-radius: 12px;
                overflow: hidden;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                transition: all 0.3s;
                cursor: pointer;
            }

            .blog-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            }

            .blog-card.featured {
                border: 2px solid var(--amarelo, #ffe206);
            }

            .blog-image {
                width: 100%;
                height: 200px;
                object-fit: cover;
            }

            .blog-content {
                padding: 1.5rem;
            }

            .blog-meta {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;
                font-size: 0.9rem;
                color: var(--cinza-escuro, #666);
            }

            .blog-category {
                background: var(--azul-escuro, #005aec);
                color: white;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 0.8rem;
                text-transform: uppercase;
            }

            .blog-title {
                font-size: 1.3rem;
                font-weight: bold;
                color: var(--azul-escuro, #005aec);
                margin-bottom: 0.5rem;
                line-height: 1.4;
            }

            .blog-excerpt {
                color: var(--cinza-escuro, #666);
                line-height: 1.6;
                margin-bottom: 1rem;
            }

            .blog-footer {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding-top: 1rem;
                border-top: 1px solid #eee;
                font-size: 0.9rem;
                color: var(--cinza-escuro, #666);
            }

            .newsletter-section {
                background: var(--azul-escuro, #005aec);
                color: white;
                padding: 4rem 0;
                text-align: center;
            }

            .newsletter-content h2 {
                font-size: 2rem;
                margin-bottom: 1rem;
            }

            .newsletter-content p {
                font-size: 1.1rem;
                margin-bottom: 2rem;
                opacity: 0.9;
            }

            .newsletter-form {
                display: flex;
                max-width: 400px;
                margin: 0 auto;
                gap: 1rem;
            }

            .newsletter-form input {
                flex: 1;
                padding: 12px 16px;
                border: none;
                border-radius: 25px;
                font-size: 16px;
            }

            .newsletter-form button {
                background: var(--verde-claro, #01d800);
                color: white;
                border: none;
                border-radius: 25px;
                padding: 12px 24px;
                font-weight: bold;
                cursor: pointer;
                transition: background 0.3s;
            }

            .newsletter-form button:hover {
                background: var(--verde-escuro, #217345);
            }

            /* Estilos da Paginação */
            .pagination {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 0.5rem;
                margin-top: 2rem;
                flex-wrap: wrap;
            }

            .page-btn {
                min-width: 44px;
                height: 44px;
                border: 2px solid #e9ecef;
                background: white;
                color: var(--cinza-escuro, #666);
                border-radius: 12px;
                font-size: 1rem;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.3s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                position: relative;
                overflow: hidden;
            }

            .page-btn:hover {
                border-color: var(--azul-escuro, #005aec);
                color: var(--azul-escuro, #005aec);
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 90, 236, 0.15);
            }

            .page-btn.active {
                background: var(--azul-escuro, #005aec);
                color: white;
                border-color: var(--azul-escuro, #005aec);
                box-shadow: 0 4px 12px rgba(0, 90, 236, 0.3);
            }

            .page-btn.active:hover {
                background: var(--azul-claro, #0066ff);
                border-color: var(--azul-claro, #0066ff);
                transform: translateY(-2px);
            }

            .page-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
            }

            .page-btn:disabled:hover {
                border-color: #e9ecef;
                color: var(--cinza-escuro, #666);
                transform: none;
                box-shadow: none;
            }

            .page-btn.nav-btn {
                min-width: 120px;
                padding: 0 1rem;
                font-size: 0.9rem;
                gap: 0.5rem;
            }

            .page-btn.nav-btn svg {
                width: 16px;
                height: 16px;
                transition: transform 0.3s ease;
            }

            .page-btn.nav-btn:hover svg {
                transform: translateX(2px);
            }

            .page-btn.nav-btn.prev:hover svg {
                transform: translateX(-2px);
            }

            .pagination-info {
                display: flex;
                align-items: center;
                gap: 1rem;
                margin-top: 1rem;
                justify-content: center;
                font-size: 0.9rem;
                color: var(--cinza-escuro, #666);
            }

            .pagination-ellipsis {
                color: var(--cinza-escuro, #666);
                font-weight: bold;
                padding: 0 0.5rem;
                display: flex;
                align-items: center;
                height: 44px;
            }

            @media (max-width: 768px) {
                .blog-grid {
                    grid-template-columns: 1fr;
                }

                .blog-filters {
                    gap: 0.25rem;
                }

                .filter-btn {
                    font-size: 12px;
                    padding: 6px 12px;
                }

                .newsletter-form {
                    flex-direction: column;
                }

                .pagination {
                    gap: 0.25rem;
                }

                .page-btn {
                    min-width: 40px;
                    height: 40px;
                    font-size: 0.9rem;
                }

                .page-btn.nav-btn {
                    min-width: 100px;
                    font-size: 0.8rem;
                }

                .pagination-info {
                    font-size: 0.8rem;
                }
            }
        `;

        const styleSheet = document.createElement('style');
        styleSheet.textContent = styles;
        document.head.appendChild(styleSheet);
    }

    setupEventListeners() {
        // Filtros
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentCategory = e.target.dataset.category;
                this.currentPage = 1;
                this.renderArticles();
            });
        });

        // Busca
        const searchInput = document.getElementById('blogSearch');
        const searchBtn = document.getElementById('searchBtn');

        searchInput.addEventListener('input', (e) => {
            this.searchTerm = e.target.value.toLowerCase();
            this.currentPage = 1;
            this.renderArticles();
        });

        searchBtn.addEventListener('click', () => {
            this.renderArticles();
        });

        // Newsletter
        const newsletterForm = document.getElementById('newsletterForm');
        if (newsletterForm) {
            newsletterForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleNewsletterSubmit(e);
            });
        }

        // Navegação por teclado para paginação
        document.addEventListener('keydown', (e) => {
            // Só funciona se não estiver digitando em um input
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                return;
            }

            const totalPages = Math.ceil(this.filterArticles().length / this.articlesPerPage);

            if (e.key === 'ArrowLeft' && this.currentPage > 1) {
                e.preventDefault();
                this.goToPage(this.currentPage - 1);
            } else if (e.key === 'ArrowRight' && this.currentPage < totalPages) {
                e.preventDefault();
                this.goToPage(this.currentPage + 1);
            }
        });
    }

    renderArticles() {
        const container = document.getElementById('blogGrid');
        const filteredArticles = this.filterArticles();
        const startIndex = (this.currentPage - 1) * this.articlesPerPage;
        const endIndex = startIndex + this.articlesPerPage;
        const articlesToShow = filteredArticles.slice(startIndex, endIndex);

        if (articlesToShow.length === 0) {
            container.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 3rem;">
                    <h3>Nenhum artigo encontrado</h3>
                    <p>Tente usar outros termos de busca ou filtros.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = articlesToShow.map(article => `
            <article class="blog-card ${article.featured ? 'featured' : ''}" onclick="location.href='post.html?id=${article.id}'">
                <img src="${article.image}" alt="${article.title}" class="blog-image" loading="lazy"
                     onerror="this.src='https://via.placeholder.com/400x250/005aec/ffffff?text=Logyc+Contabilidade'">
                <div class="blog-content">
                    <div class="blog-meta">
                        <span class="blog-category">${this.getCategoryName(article.category)}</span>
                        <span class="blog-date">${this.formatDate(article.date)}</span>
                    </div>
                    <h3 class="blog-title">${article.title}</h3>
                    <p class="blog-excerpt">${article.excerpt}</p>
                    <div class="blog-footer">
                        <span>Por ${article.author}</span>
                        <span>${article.readTime} de leitura</span>
                    </div>
                </div>
            </article>
        `).join('');

        this.renderPagination(filteredArticles.length);
    }

    filterArticles() {
        return this.articles.filter(article => {
            const matchesCategory = this.currentCategory === 'all' || article.category === this.currentCategory;
            const matchesSearch = !this.searchTerm ||
                article.title.toLowerCase().includes(this.searchTerm) ||
                article.excerpt.toLowerCase().includes(this.searchTerm) ||
                article.tags.some(tag => tag.includes(this.searchTerm));

            return matchesCategory && matchesSearch;
        });
    }

    renderPagination(totalArticles) {
        const container = document.getElementById('blogPagination');
        const totalPages = Math.ceil(totalArticles / this.articlesPerPage);

        if (totalPages <= 1) {
            container.innerHTML = '';
            return;
        }

        let paginationHTML = '<div class="pagination">';

        // Botão Anterior
        const prevDisabled = this.currentPage === 1;
        paginationHTML += `
            <button class="page-btn nav-btn prev ${prevDisabled ? 'disabled' : ''}"
                    onclick="window.blogSystem.goToPage(${this.currentPage - 1})"
                    ${prevDisabled ? 'disabled' : ''}>
                <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
                Anterior
            </button>
        `;

        // Lógica para mostrar páginas
        const maxVisiblePages = 5;
        let startPage = Math.max(1, this.currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        // Ajustar se não há páginas suficientes no final
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        // Primeira página e reticências se necessário
        if (startPage > 1) {
            paginationHTML += `
                <button class="page-btn" onclick="window.blogSystem.goToPage(1)">1</button>
            `;
            if (startPage > 2) {
                paginationHTML += '<span class="pagination-ellipsis">...</span>';
            }
        }

        // Páginas numeradas
        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <button class="page-btn ${i === this.currentPage ? 'active' : ''}"
                        onclick="window.blogSystem.goToPage(${i})">${i}</button>
            `;
        }

        // Última página e reticências se necessário
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHTML += '<span class="pagination-ellipsis">...</span>';
            }
            paginationHTML += `
                <button class="page-btn" onclick="window.blogSystem.goToPage(${totalPages})">${totalPages}</button>
            `;
        }

        // Botão Próximo
        const nextDisabled = this.currentPage === totalPages;
        paginationHTML += `
            <button class="page-btn nav-btn next ${nextDisabled ? 'disabled' : ''}"
                    onclick="window.blogSystem.goToPage(${this.currentPage + 1})"
                    ${nextDisabled ? 'disabled' : ''}>
                Próximo
                <svg fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                </svg>
            </button>
        `;

        paginationHTML += '</div>';

        // Informações da paginação
        const startItem = (this.currentPage - 1) * this.articlesPerPage + 1;
        const endItem = Math.min(this.currentPage * this.articlesPerPage, totalArticles);

        paginationHTML += `
            <div class="pagination-info">
                <span>Mostrando ${startItem} a ${endItem} de ${totalArticles} artigos</span>
                <span>•</span>
                <span>Página ${this.currentPage} de ${totalPages}</span>
            </div>
        `;

        container.innerHTML = paginationHTML;
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.filterArticles().length / this.articlesPerPage);

        // Validação da página
        if (page < 1 || page > totalPages || page === this.currentPage) {
            return;
        }

        // Adicionar efeito de loading
        const paginationContainer = document.getElementById('blogPagination');
        if (paginationContainer) {
            paginationContainer.style.opacity = '0.6';
            paginationContainer.style.pointerEvents = 'none';
        }

        // Pequeno delay para mostrar o efeito de loading
        setTimeout(() => {
            this.currentPage = page;
            this.renderArticles();

            // Restaurar paginação
            if (paginationContainer) {
                paginationContainer.style.opacity = '1';
                paginationContainer.style.pointerEvents = 'auto';
            }

            // Scroll suave para o topo da seção do blog
            document.querySelector('.blog-section').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // Rastrear mudança de página
            if (window.trackEvent) {
                window.trackEvent('blog_pagination', {
                    page: page,
                    category: this.currentCategory,
                    search_term: this.searchTerm
                });
            }
        }, 150);
    }

    getCategoryName(category) {
        const names = {
            'mei': 'MEI',
            'abertura': 'Abertura',
            'fiscal': 'Fiscal',
            'pessoal': 'Pessoal',
            'dicas': 'Dicas'
        };
        return names[category] || category;
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('pt-BR');
    }

    handleNewsletterSubmit(e) {
        const email = e.target.querySelector('input[type="email"]').value;

        if (window.notifications) {
            window.notifications.success('Email cadastrado com sucesso! Você receberá nossas novidades em breve.');
        }

        // Rastrear inscrição
        if (window.trackEvent) {
            window.trackEvent('newsletter_signup', { email_domain: email.split('@')[1] });
        }

        e.target.reset();
    }
}

// Adicionar método global para abrir artigos
window.openArticle = function(articleId) {
    if (window.trackEvent) {
        window.trackEvent('blog_article_click', { article_id: articleId });
    }

    // Redirecionar para página do post
    window.location.href = `post.html?id=${articleId}`;
};

// Inicializar apenas na página do blog
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.pathname.includes('blog.html')) {
        window.blogSystem = new BlogSystem();
    }
});

// Exportar para uso global
window.BlogSystem = BlogSystem;
