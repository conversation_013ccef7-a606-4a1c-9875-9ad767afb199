// Sistema de Analytics Avançado - Logyc Contabilidade
class AdvancedAnalytics {
    constructor() {
        this.sessionId = this.generateSessionId();
        this.userId = this.getUserId();
        this.pageLoadTime = Date.now();
        this.events = [];
        this.heatmapData = [];
        this.scrollDepth = 0;
        this.timeOnPage = 0;
        this.interactions = 0;
        this.init();
    }
    
    init() {
        this.setupUserTracking();
        this.setupHeatmapTracking();
        this.setupScrollTracking();
        this.setupFormTracking();
        this.setupClickTracking();
        this.setupTimeTracking();
        this.setupConversionTracking();
        this.setupErrorTracking();
        this.startSession();
    }
    
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    getUserId() {
        let userId = localStorage.getItem('logyc_user_id');
        if (!userId) {
            userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('logyc_user_id', userId);
        }
        return userId;
    }
    
    startSession() {
        this.trackEvent('session_start', {
            session_id: this.sessionId,
            user_id: this.userId,
            page: window.location.pathname,
            referrer: document.referrer,
            user_agent: navigator.userAgent,
            screen_resolution: `${screen.width}x${screen.height}`,
            viewport: `${window.innerWidth}x${window.innerHeight}`,
            timestamp: new Date().toISOString()
        });
    }
    
    setupUserTracking() {
        // Detectar tipo de dispositivo
        const deviceType = this.getDeviceType();
        const browserInfo = this.getBrowserInfo();
        
        this.trackEvent('user_info', {
            device_type: deviceType,
            browser: browserInfo.name,
            browser_version: browserInfo.version,
            os: this.getOS(),
            language: navigator.language,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
        });
        
        // Detectar se é visitante recorrente
        const visitCount = parseInt(localStorage.getItem('logyc_visit_count') || '0') + 1;
        localStorage.setItem('logyc_visit_count', visitCount.toString());
        
        this.trackEvent('visit_info', {
            visit_count: visitCount,
            is_returning: visitCount > 1,
            last_visit: localStorage.getItem('logyc_last_visit'),
            session_id: this.sessionId
        });
        
        localStorage.setItem('logyc_last_visit', new Date().toISOString());
    }
    
    setupHeatmapTracking() {
        let clickTimeout;
        
        document.addEventListener('click', (e) => {
            const rect = e.target.getBoundingClientRect();
            const x = e.clientX;
            const y = e.clientY;
            
            this.heatmapData.push({
                x: x,
                y: y,
                element: e.target.tagName,
                className: e.target.className,
                id: e.target.id,
                timestamp: Date.now(),
                page: window.location.pathname
            });
            
            this.trackEvent('heatmap_click', {
                x: x,
                y: y,
                element: e.target.tagName,
                element_text: e.target.textContent?.substring(0, 50),
                page_section: this.getPageSection(e.target)
            });
        });
        
        // Tracking de movimento do mouse (sampling)
        let mouseMoveCount = 0;
        document.addEventListener('mousemove', (e) => {
            mouseMoveCount++;
            if (mouseMoveCount % 50 === 0) { // Sample every 50th movement
                this.trackEvent('mouse_movement', {
                    x: e.clientX,
                    y: e.clientY,
                    timestamp: Date.now()
                });
            }
        });
    }
    
    setupScrollTracking() {
        let maxScroll = 0;
        let scrollMilestones = [25, 50, 75, 90, 100];
        let reachedMilestones = [];
        
        window.addEventListener('scroll', () => {
            const scrollPercent = Math.round(
                (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100
            );
            
            if (scrollPercent > maxScroll) {
                maxScroll = scrollPercent;
                this.scrollDepth = maxScroll;
            }
            
            // Track milestones
            scrollMilestones.forEach(milestone => {
                if (scrollPercent >= milestone && !reachedMilestones.includes(milestone)) {
                    reachedMilestones.push(milestone);
                    this.trackEvent('scroll_milestone', {
                        milestone: milestone,
                        time_to_reach: Date.now() - this.pageLoadTime
                    });
                }
            });
        });
    }
    
    setupFormTracking() {
        // Track form interactions
        document.querySelectorAll('form').forEach(form => {
            const formId = form.id || form.className || 'unknown_form';
            let formStartTime = null;
            let fieldInteractions = {};
            
            form.addEventListener('focusin', (e) => {
                if (!formStartTime) {
                    formStartTime = Date.now();
                    this.trackEvent('form_start', {
                        form_id: formId,
                        first_field: e.target.name || e.target.id
                    });
                }
                
                this.trackEvent('field_focus', {
                    form_id: formId,
                    field_name: e.target.name || e.target.id,
                    field_type: e.target.type
                });
            });
            
            form.addEventListener('input', (e) => {
                const fieldName = e.target.name || e.target.id;
                if (!fieldInteractions[fieldName]) {
                    fieldInteractions[fieldName] = 0;
                }
                fieldInteractions[fieldName]++;
            });
            
            form.addEventListener('submit', (e) => {
                const completionTime = Date.now() - formStartTime;
                this.trackEvent('form_submit', {
                    form_id: formId,
                    completion_time: completionTime,
                    field_interactions: fieldInteractions,
                    total_fields: form.querySelectorAll('input, textarea, select').length
                });
            });
            
            // Track form abandonment
            document.addEventListener('beforeunload', () => {
                if (formStartTime && !form.querySelector('[type="submit"]:focus')) {
                    this.trackEvent('form_abandon', {
                        form_id: formId,
                        time_spent: Date.now() - formStartTime,
                        field_interactions: fieldInteractions
                    });
                }
            });
        });
    }
    
    setupClickTracking() {
        // Track specific element clicks
        const trackableElements = [
            'button',
            'a',
            '.btn',
            '.whatsapp-button',
            '.company-type',
            '.quick-action',
            '.faq-question',
            '.testimonial-nav'
        ];
        
        trackableElements.forEach(selector => {
            document.addEventListener('click', (e) => {
                if (e.target.matches(selector) || e.target.closest(selector)) {
                    const element = e.target.matches(selector) ? e.target : e.target.closest(selector);
                    
                    this.trackEvent('element_click', {
                        element_type: selector,
                        element_text: element.textContent?.substring(0, 50),
                        element_id: element.id,
                        element_class: element.className,
                        page_section: this.getPageSection(element)
                    });
                }
            });
        });
    }
    
    setupTimeTracking() {
        // Track time spent on page
        setInterval(() => {
            this.timeOnPage += 1000;
        }, 1000);
        
        // Track time spent in specific sections
        const sections = document.querySelectorAll('section[id]');
        const sectionTimes = {};
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                const sectionId = entry.target.id;
                
                if (entry.isIntersecting) {
                    sectionTimes[sectionId] = Date.now();
                } else if (sectionTimes[sectionId]) {
                    const timeSpent = Date.now() - sectionTimes[sectionId];
                    this.trackEvent('section_time', {
                        section_id: sectionId,
                        time_spent: timeSpent
                    });
                    delete sectionTimes[sectionId];
                }
            });
        }, { threshold: 0.5 });
        
        sections.forEach(section => observer.observe(section));
    }
    
    setupConversionTracking() {
        // Track conversion funnel
        const conversionSteps = {
            'page_view': 1,
            'calculator_usage': 2,
            'form_start': 3,
            'form_submit': 4,
            'whatsapp_click': 5
        };
        
        let currentStep = 1;
        
        // Override trackEvent to detect funnel progression
        const originalTrackEvent = this.trackEvent.bind(this);
        this.trackEvent = (eventName, data) => {
            if (conversionSteps[eventName] && conversionSteps[eventName] > currentStep) {
                currentStep = conversionSteps[eventName];
                originalTrackEvent('funnel_progression', {
                    step: currentStep,
                    step_name: eventName,
                    time_to_step: Date.now() - this.pageLoadTime
                });
            }
            return originalTrackEvent(eventName, data);
        };
    }
    
    setupErrorTracking() {
        // Track JavaScript errors
        window.addEventListener('error', (e) => {
            this.trackEvent('javascript_error', {
                message: e.message,
                filename: e.filename,
                line: e.lineno,
                column: e.colno,
                stack: e.error?.stack
            });
        });
        
        // Track unhandled promise rejections
        window.addEventListener('unhandledrejection', (e) => {
            this.trackEvent('promise_rejection', {
                reason: e.reason?.toString(),
                stack: e.reason?.stack
            });
        });
        
        // Track form validation errors
        document.addEventListener('invalid', (e) => {
            this.trackEvent('validation_error', {
                field_name: e.target.name || e.target.id,
                field_type: e.target.type,
                validation_message: e.target.validationMessage,
                form_id: e.target.closest('form')?.id
            });
        });
    }
    
    trackEvent(eventName, data = {}) {
        const event = {
            event: eventName,
            timestamp: new Date().toISOString(),
            session_id: this.sessionId,
            user_id: this.userId,
            page: window.location.pathname,
            page_title: document.title,
            ...data
        };
        
        this.events.push(event);
        
        // Send to analytics services
        this.sendToAnalytics(event);
        
        // Store locally for backup
        this.storeEventLocally(event);
        
        console.log('Analytics Event:', event);
    }
    
    sendToAnalytics(event) {
        // Google Analytics 4
        if (typeof gtag !== 'undefined') {
            gtag('event', event.event, {
                custom_parameter_1: JSON.stringify(event),
                session_id: event.session_id,
                user_id: event.user_id
            });
        }
        
        // Facebook Pixel
        if (typeof fbq !== 'undefined') {
            fbq('trackCustom', event.event, event);
        }
        
        // Custom analytics endpoint (when available)
        if (window.LogycConfig?.analytics?.endpoint) {
            fetch(window.LogycConfig.analytics.endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(event)
            }).catch(err => console.warn('Analytics send failed:', err));
        }
    }
    
    storeEventLocally(event) {
        const stored = JSON.parse(localStorage.getItem('logyc_analytics') || '[]');
        stored.push(event);
        
        // Keep only last 100 events
        if (stored.length > 100) {
            stored.splice(0, stored.length - 100);
        }
        
        localStorage.setItem('logyc_analytics', JSON.stringify(stored));
    }
    
    getDeviceType() {
        const width = window.innerWidth;
        if (width < 768) return 'mobile';
        if (width < 1024) return 'tablet';
        return 'desktop';
    }
    
    getBrowserInfo() {
        const ua = navigator.userAgent;
        let browser = 'Unknown';
        let version = 'Unknown';
        
        if (ua.includes('Chrome')) {
            browser = 'Chrome';
            version = ua.match(/Chrome\/(\d+)/)?.[1] || 'Unknown';
        } else if (ua.includes('Firefox')) {
            browser = 'Firefox';
            version = ua.match(/Firefox\/(\d+)/)?.[1] || 'Unknown';
        } else if (ua.includes('Safari')) {
            browser = 'Safari';
            version = ua.match(/Version\/(\d+)/)?.[1] || 'Unknown';
        } else if (ua.includes('Edge')) {
            browser = 'Edge';
            version = ua.match(/Edge\/(\d+)/)?.[1] || 'Unknown';
        }
        
        return { name: browser, version: version };
    }
    
    getOS() {
        const ua = navigator.userAgent;
        if (ua.includes('Windows')) return 'Windows';
        if (ua.includes('Mac')) return 'macOS';
        if (ua.includes('Linux')) return 'Linux';
        if (ua.includes('Android')) return 'Android';
        if (ua.includes('iOS')) return 'iOS';
        return 'Unknown';
    }
    
    getPageSection(element) {
        const section = element.closest('section');
        return section?.id || section?.className || 'unknown';
    }
    
    // Public methods for manual tracking
    trackConversion(type, value = 0) {
        this.trackEvent('conversion', {
            conversion_type: type,
            conversion_value: value,
            time_to_conversion: Date.now() - this.pageLoadTime
        });
    }
    
    trackGoal(goalName, goalValue = 1) {
        this.trackEvent('goal_completion', {
            goal_name: goalName,
            goal_value: goalValue
        });
    }
    
    getSessionSummary() {
        return {
            session_id: this.sessionId,
            user_id: this.userId,
            time_on_page: this.timeOnPage,
            scroll_depth: this.scrollDepth,
            interactions: this.interactions,
            events_count: this.events.length,
            page_views: this.events.filter(e => e.event === 'page_view').length,
            conversions: this.events.filter(e => e.event === 'conversion').length
        };
    }
    
    exportData() {
        return {
            session: this.getSessionSummary(),
            events: this.events,
            heatmap: this.heatmapData
        };
    }
}

// Initialize advanced analytics
document.addEventListener('DOMContentLoaded', function() {
    window.advancedAnalytics = new AdvancedAnalytics();
    
    // Track page view
    window.advancedAnalytics.trackEvent('page_view', {
        page_type: document.body.className || 'unknown',
        load_time: Date.now() - performance.timing.navigationStart
    });
});

// Export for global use
window.AdvancedAnalytics = AdvancedAnalytics;
